import crypto from "crypto";
import {
  VALID_FILE_TYPES,
  SUPPORTED_AUDIO_FORMATS,
  MIME_TO_EXTENSION,
} from "@/constants/file";
import { trackEvent } from "@/lib/analytics";
import { UPLOAD_CONFIG } from "@/config/upload";

// HTML5 Audio 错误代码映射
const getMediaErrorMessage = (errorCode) => {
  const errorMessages = {
    1: "MEDIA_ERR_ABORTED - The fetching process for the media resource was aborted by the user agent",
    2: "MEDIA_ERR_NETWORK - A network error of some description caused the user agent to stop fetching the media resource",
    3: "MEDIA_ERR_DECODE - An error of some description occurred while decoding the media resource",
    4: "MEDIA_ERR_SRC_NOT_SUPPORTED - The media resource indicated by the src attribute was not suitable",
  };
  return errorMessages[errorCode] || `Unknown media error code: ${errorCode}`;
};

export const isValidFileType = (file) => {
  // 首先检查 MIME 类型
  if (file.type && VALID_FILE_TYPES.includes(file.type)) {
    return true;
  }

  // 如果 MIME 类型为空或不支持，回退到文件扩展名检测
  const fileExtension =
    file.name && file.name.includes(".")
      ? file.name.split(".").pop().toLowerCase()
      : "no_extension";

  if (
    fileExtension !== "no_extension" &&
    SUPPORTED_AUDIO_FORMATS.includes(fileExtension)
  ) {
    return true;
  }

  // 文件类型不支持，记录打点用于观察是否有误伤

  trackEvent("unsupported_file_type", {
    mimeType: file.type || "empty",
    fileExtension: fileExtension,
    fileName: file.name || "unknown",
    fileSize: file.size || 0,
  });

  return false;
};

/**
 * 验证文件大小
 *
 * 统一的文件大小验证逻辑，用于所有上传引擎：
 * - 检查文件是否过小（< 1KB）
 * - 检查文件是否过大（> 5GB）- 完全阻止上传
 * - 对 2GB-5GB 的文件显示警告但允许上传
 *
 * @param {File} file - 要验证的文件
 * @param {Function} t - 国际化翻译函数 (可选)
 */
export const validateFileSize = (file, t = null) => {
  const twoGB = 2 * 1024 * 1024 * 1024; // 2GB in bytes
  const fiveGB = 5 * 1024 * 1024 * 1024; // 5GB in bytes

  // 检查文件大小是否为 0 (使用配置的最小文件大小)
  if (file.size < UPLOAD_CONFIG.minFileSize) {
    return {
      isValid: false,
      error: "emptyFile",
      // 如果有翻译函数，使用国际化文案，否则使用默认英文
      message: t
        ? t("fileUploadStatus.error.tooSmall", {
            fileName: file.name,
            size: file.size,
          })
        : `File "${file.name}" is too small (${file.size} bytes). Please select a valid audio file.`,
    };
  }

  // 检查文件大小是否超过5GB限制 - 完全阻止上传
  if (file.size > fiveGB) {
    const fileSizeGB = (file.size / (1024 * 1024 * 1024)).toFixed(2);
    return {
      isValid: false,
      error: "fileSizeExceeded",
      // 如果有翻译函数，使用国际化文案，否则使用默认英文
      message: t
        ? t("fileUploadStatus.error.tooLarge", {
            fileName: file.name,
            size: fileSizeGB,
          })
        : `File "${file.name}" is too large (${fileSizeGB} GB). Maximum file size is 5GB.`,
      showTutorialLink: true,
    };
  }

  // 检查文件大小是否在2GB-5GB之间 - 显示警告但允许上传
  if (file.size > twoGB && file.size <= fiveGB) {
    const fileSizeGB = (file.size / (1024 * 1024 * 1024)).toFixed(2);
    return {
      isValid: true,
      error: "fileSizeWarning",
      // 如果有翻译函数，使用国际化文案，否则使用默认英文
      message: t
        ? t("fileUploadStatus.error.largeFile", {
            fileName: file.name,
            size: fileSizeGB,
          })
        : `File "${file.name}" is large (${fileSizeGB} GB). Consider extracting audio for faster upload.`,
      showTutorialLink: true,
      isWarning: true,
    };
  }

  return {
    isValid: true,
    error: null,
    message: null,
  };
};

export const calculateFileDuration = async (file) => {
  // 首先尝试使用 HTML5 Audio 元素
  try {
    const duration = await new Promise((resolve, reject) => {
      const media = new Audio(URL.createObjectURL(file));

      const timeout = setTimeout(() => {
        cleanup();
        const timeoutError = new Error(
          `Audio loading timeout for file: ${file.name}`
        );
        timeoutError.code = "TIMEOUT";
        timeoutError.originalError = null; // 超时没有原始错误
        reject(timeoutError);
      }, 10000); // 10秒超时

      const cleanup = () => {
        clearTimeout(timeout);
        URL.revokeObjectURL(media.src);
        media.removeEventListener("loadedmetadata", onLoadedMetadata);
        media.removeEventListener("error", onError);
      };

      const onLoadedMetadata = () => {
        console.log(
          `Duration loaded with HTML5 Audio: ${media.duration}s for file: ${file.name}`
        );
        cleanup();
        resolve(media.duration);
      };

      const onError = (event) => {
        console.warn("HTML5 Audio loading failed:", event);
        cleanup();

        // 创建包含原始错误信息的错误对象
        const audioError = new Error(
          `HTML5 Audio loading failed for file: ${file.name}`
        );
        audioError.code = "AUDIO_ERROR";
        audioError.originalEvent = event;

        // 尝试提取更多错误信息
        if (media.error) {
          audioError.mediaError = {
            code: media.error.code,
            message:
              media.error.message || getMediaErrorMessage(media.error.code),
          };
        }

        reject(audioError);
      };

      media.addEventListener("loadedmetadata", onLoadedMetadata);
      media.addEventListener("error", onError);
      media.load();
    });

    return duration;
  } catch (htmlAudioError) {
    console.warn(
      "HTML5 Audio failed, will let backend handle duration calculation:",
      htmlAudioError?.mediaError?.message
    );

    // Track HTML5 Audio duration calculation failure
    trackEvent("html5_audio_duration_failed", {
      fileType: file.type,
      errorType: htmlAudioError?.mediaError?.code || "unknown",
      errorMessage: htmlAudioError?.mediaError?.message,
    });

    // HTML5 Audio 失败时返回 null，让后端处理时长计算
    console.log(
      `Returning null duration for ${file.name}, backend will handle preprocessing`
    );
    return null;
  }
};

// 生成一个基于文件名、大小和时间戳的伪哈希
const generatePseudoHash = (file) => {
  // 创建一个基于文件属性的字符串
  const fileInfo = `${file.name}-${file.size}-${
    file.lastModified
  }-${Date.now()}`;
  // 使用 MD5 哈希这个字符串
  const hash = crypto.createHash("md5").update(fileInfo).digest("base64");
  return hash;
};

export const calculateFileChecksum = (file) => {
  // 检查文件大小是否超过 2GB 限制
  const TWO_GB = 2 * 1024 * 1024 * 1024; // 2GB in bytes

  if (file.size >= TWO_GB) {
    console.log(
      `File size (${(file.size / (1024 * 1024)).toFixed(
        2
      )} MB) exceeds the 2GB WebAssembly limit. Using pseudo-hash instead.`
    );
    // 对于大于 2GB 的文件，使用基于文件属性的伪哈希
    return Promise.resolve(generatePseudoHash(file));
  }

  return new Promise((resolve) => {
    try {
      const reader = new FileReader();

      reader.onload = () => {
        try {
          const buffer = Buffer.from(reader.result);
          const hash = crypto.createHash("md5").update(buffer).digest("base64");
          resolve(hash);
        } catch (error) {
          console.error("Error calculating checksum:", error);
          // 如果计算哈希失败，使用伪哈希作为备选方案
          console.warn("Falling back to pseudo-hash due to error:", error);
          resolve(generatePseudoHash(file));
        }
      };

      reader.onerror = (error) => {
        console.error("FileReader error:", error);
        // 对于读取错误，使用伪哈希
        console.warn("FileReader error. Using pseudo-hash instead.");
        resolve(generatePseudoHash(file));
      };

      reader.readAsArrayBuffer(file);
    } catch (error) {
      console.error("Error starting file read:", error);
      // 对于任何其他错误，也使用伪哈希
      console.warn("Error starting file read. Using pseudo-hash instead.");
      resolve(generatePseudoHash(file));
    }
  });
};

export const getFileExtension = (file) => {
  // 优先使用 MIME 类型
  if (file.type && MIME_TO_EXTENSION[file.type]) {
    console.log(
      `Using MIME type for file extension: ${MIME_TO_EXTENSION[file.type]}`
    );
    return MIME_TO_EXTENSION[file.type];
  }

  // 回退到文件名扩展名
  if (file.name && file.name.includes(".")) {
    const extension = file.name.split(".").pop().toLowerCase();

    // 验证扩展名是否在支持的格式列表中
    if (SUPPORTED_AUDIO_FORMATS.includes(extension)) {
      console.log(`Using file extension: ${extension}`);
      return extension;
    }
  }

  // 如果都无法确定，尝试从 MIME 类型推断
  if (file.type) {
    if (file.type.startsWith("audio/")) {
      console.log(`Unknown audio type: ${file.type}. Defaulting to mp3.`);
      return "mp3"; // 默认音频格式
    }
    if (file.type.startsWith("video/")) {
      console.log(`Unknown video type: ${file.type}. Defaulting to mp4.`);
      return "mp4"; // 默认视频格式
    }
  }

  // 最后的回退选项
  return "mp3";
};
