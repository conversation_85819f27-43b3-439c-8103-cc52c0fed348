{"loading": "Laden...", "meta": {"title": "Kostenloser YouTube Transkript-Extractor | YouTube-Transkripte erstellen und herunterladen", "description": "Extrahieren Sie Video-Transkripte mit einem Klick. Laden Sie Untertitel in mehreren Formaten herunter (SRT, VTT, TXT, CSV). Keine Registrierung erford<PERSON>lich, schnell und sicher.", "ogTitle": "YouTube Transkriptgenerator | Extrahieren und Herunterladen von Video-Transkripten kostenlos", "ogDescription": "Generieren Sie YouTube-Transkripte sofort kostenlos. Extrahieren Sie Video-Transkripte mit nur einem Klick. Laden Sie Untertitel in mehreren Formaten herunter (SRT, VTT, TXT, CSV). Keine Registrierung erforderlich, schnell und sicher.", "ogImageAlt": "UniScribe YouTube Transkript-Extractor", "twitterTitle": "YouTube Transkriptgenerator | Extrahieren und Herunterladen von Video-Transkripten kostenlos", "twitterDescription": "Extrahieren Sie YouTube-Video-Transkripte einfach. Laden Sie Untertitel in mehreren Formaten herunter (SRT, VTT, TXT, CSV). <PERSON>ine Regis<PERSON> erford<PERSON>lich, schnell und sicher.", "schemaTitle": "YouTube-Transkript-Extractor", "schemaDescription": "Extrahieren Sie YouTube-Video-Transkripte einfach. Laden Sie Untertitel in mehreren Formaten herunter (SRT, VTT, TXT, CSV). <PERSON>ine Regis<PERSON> erford<PERSON>lich, schnell und sicher."}, "hero": {"title": "YouTube-Transkrip<PERSON> kostenlos extra<PERSON>eren", "description": "Konvertieren Sie ein YouTube-Video mühelos in ein Transkript, kopieren Sie das generierte YouTube-Transkript und laden Si<PERSON> es mit einem Klick herunter."}, "features": {"extraction": {"title": "Kostenlose Transkriptextraktion", "description": "Vollständige Video-Transkripte mit Zeitstempeln extrahieren"}, "formats": {"title": "Mehrere Exportformate", "description": "Herunterladen im TXT-, SRT-, VTT- und CSV-Format"}, "instant": {"title": "Transkripte sofort erstellen", "description": "Erhalten Sie Transkripte sofort ohne Registrierung."}}, "buttons": {"getTranscript": "Transkript abrufen", "goToTranscribe": "<PERSON><PERSON><PERSON> zu Transkribieren", "export": "Exportieren"}, "preview": {"title": "Video-Vors<PERSON>u"}, "transcript": {"title": "Transkript", "includeTimestamps": "Zeitstempel einfügen"}, "faq": {"what": {"question": "Was ist der YouTube Transkript-Extractor?", "answer": "YouTube Transcript Extractor ist ein kostenloses Tool, mit dem Sie Transkripte aus YouTube-Videos extrahieren und herunterladen können."}, "howTo": {"question": "Wie verwende ich den YouTube Transcript Extractor?", "answer": "<PERSON>ügen Si<PERSON> einfach die YouTube-Video-URL ein und klicken Sie auf 'Transkript abrufen'."}, "free": {"question": "Ist der YouTube Transcript Extractor kostenlos zu verwenden?", "answer": "<PERSON><PERSON>, die grundlegende Funktion zur Transkriptionserstellung ist vollständig kostenlos."}, "formats": {"question": "Kann ich YouTube-Video-Transkripte in verschiedenen Formaten herunterladen?", "answer": "<PERSON><PERSON>, <PERSON><PERSON> können Transkripte im .txt-, .srt-, .vtt- und .csv-Format herunterladen."}, "private": {"question": "Kann ich Transkripte aus privaten YouTube-Videos extrahieren?", "answer": "<PERSON><PERSON>, Transkripte können nur aus öffentlichen Videos extrahiert werden."}, "limit": {"question": "Gibt es eine Begrenzung für die Anzahl der Transkripte, die ich extrahieren kann?", "answer": "<PERSON><PERSON>, es gibt keine Begrenzung für die Anzahl der Transkripte, die Si<PERSON> extrahieren können."}, "account": {"question": "Muss ich ein Konto erstellen, um den YouTube Transcript Extractor zu verwenden?", "answer": "Die grundlegende Ansicht von Transkripten ist ohne ein Konto kostenlos. Funktionen wie das Kopieren und Herunterladen von Untertiteln erfordern jedoch die Anmeldung mit einem kostenlosen Konto."}, "extract": {"question": "Kann ich Transkripte von YouTube-Videos extrahieren?", "answer": "Wenn das Video bereits vorhandene Transkripte hat, können wir diese direkt extrahieren. Andernfalls können Sie die Transkriptionsfunktion von Uniscribe für YouTube verwenden, um das Transkript zu erstellen."}}, "upgradeNotice": "Entschuldigung für die Unterbrechung! Die YouTube-Transkription wird aktualisiert und wird in Kürze zurückkehren."}