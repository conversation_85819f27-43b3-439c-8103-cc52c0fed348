export const KEYWORD_PAGES = {
  "voice-to-text": {
    title: "เครื่องแปลงเสียงเป็นข้อความ | การรู้จำเสียงพูดทันที",
    displayTitle: "เครื่องแปลงเสียงเป็นข้อความ",
    description:
      "แปลงการบันทึกเสียงเป็นข้อความด้วยความแม่นยำระดับมืออาชีพ เหมาะสำหรับการประชุม การบรรยาย และบันทึกเสียง.",
  },
  "mp3-to-text": {
    title: "เครื่องมือแปลงเสียง MP3 เป็นข้อความออนไลน์ฟรี - ไม่ต้องดาวน์โหลด",
    displayTitle: "เครื่องมือแปลง MP3 เป็นข้อความออนไลน์ที่ขับเคลื่อนด้วย AI",
    description:
      "แปลงไฟล์เสียง MP3 เป็นข้อความในไม่กี่นาที รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP3 เป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากเนื้อหาโดยอิงจากเทคโนโลยี AI",
    howToUse: {
      title: "วิธีแปลง MP3 เป็นข้อความในสามขั้นตอน?",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP3",
        step2: "คลิกที่แปลงข้อความ",
        step3: "ส่งออกผลลัพธ์ข้อความ",
      },
    },
    features: [
      {
        title: "แปลงเสียง MP3 เป็นข้อความออนไลน์ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MP3",
      },
      {
        title: "ส่งออกการถอดเสียงในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 เป็น TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 เป็น Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 เป็น PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 เป็น SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "MP3 เป็น VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
    faqs: [
      {
        question: "วิธีแปลงเสียง MP3 เป็นข้อความออนไลน์ฟรี?",
        answer:
          "1) อัปโหลดไฟล์ MP3 ของคุณ 2) คลิกปุ่ม 'เข้าสู่ระบบเพื่อแปลงข้อความ' 3) รอให้การถอดเสียงเสร็จสิ้น 4) ดาวน์โหลดข้อความของคุณในรูปแบบที่คุณต้องการ (TXT, Word, PDF, ฯลฯ)",
      },
      {
        question: "มีภาษาใดบ้างที่รองรับการแปลง MP3 เป็นข้อความ?",
        answer:
          "UniScribe รองรับ 98 ภาษาในการแปลง MP3 เป็นข้อความ รวมถึงภาษาอังกฤษ สเปน ฝรั่งเศส เยอรมัน จีน ญี่ปุ่น เกาหลี และอีกมากมาย ระบบ AI จะตรวจจับภาษาของไฟล์เสียงของคุณโดยอัตโนมัติ",
      },
      {
        question: "การแปลง MP3 เป็นข้อความมีความแม่นยำแค่ไหน?",
        answer:
          "UniScribe ใช้เทคโนโลยี AI ขั้นสูงเพื่อให้ได้ความแม่นยำสูงในการแปลง MP3 เป็นข้อความ ความแม่นยำมักอยู่ในช่วง 90-99% ขึ้นอยู่กับคุณภาพเสียง ความชัดเจนของผู้พูด และเสียงรบกวนพื้นหลัง",
      },
      {
        question: "รูปแบบผลลัพธ์ใดบ้างที่มีให้สำหรับการถอดเสียง?",
        answer:
          "UniScribe มีรูปแบบการส่งออกหลายรูปแบบรวมถึง TXT, Word (DOCX), PDF, SRT (คำบรรยาย), VTT (คำบรรยายเว็บ) และ CSV คุณสามารถเลือกฟอร์แมตที่เหมาะสมกับความต้องการของคุณที่สุด",
      },
      {
        question: "เครื่องมือแปลง MP3 เป็นข้อความใช้งานฟรีหรือไม่?",
        answer:
          "ใช่, UniScribe มีระดับฟรีที่อนุญาตให้คุณแปลง MP3 เป็นข้อความ ฟีเจอร์พรีเมียมมีให้สำหรับผู้ใช้ที่ต้องการความสามารถเพิ่มเติม เช่น การประมวลผลแบบกลุ่ม การประมวลผลลำดับความสำคัญ หรือฟีเจอร์ AI ขั้นสูง",
      },
      {
        question: "ใช้เวลานานเท่าไหร่ในการแปลง MP3 เป็นข้อความ?",
        answer:
          "ความเร็วในการแปลงขึ้นอยู่กับขนาดและความยาวของไฟล์ โดยทั่วไปแล้วไฟล์ MP3 ขนาด 1 ชั่วโมงสามารถแปลงเป็นข้อความได้ในเวลาน้อยกว่า 1 นาทีด้วย UniScribe เวลาประมวลผลอาจแตกต่างกันไปตามภาระงานของเซิร์ฟเวอร์และคุณภาพไฟล์",
      },
      {
        question: "ไฟล์ MP3 ของฉันปลอดภัยในระหว่างกระบวนการแปลงหรือไม่?",
        answer:
          "ใช่, UniScribe ให้ความสำคัญกับความปลอดภัย ไฟล์ทั้งหมดที่อัปโหลดจะถูกเข้ารหัสด้วย SSL/TLS และไฟล์จะถูกลบโดยอัตโนมัติจากเซิร์ฟเวอร์ของเราหลังจากการประมวลผล เราจะไม่แชร์ไฟล์ของคุณกับบุคคลที่สาม",
      },
    ],
  },
  "mp3-to-vtt": {
    title: "แปลง MP3 เป็น VTT ฟรีออนไลน์",
    displayTitle: "แปลง MP3 เป็น VTT ฟรีออนไลน์",
    description:
      "UniScribe เป็นบริการถอดความออนไลน์ฟรีที่สามารถแปลงไฟล์ mp3 เป็นรูปแบบคำบรรยาย vtt ที่ถูกต้องได้อย่างรวดเร็ว รองรับ 98 ภาษา นอกจากนี้ยังสรุปเนื้อหาและประเด็นสำคัญของไฟล์ของคุณและสร้างแผนผังความคิดเพื่อช่วยให้คุณดึงข้อมูลสำคัญออกมาได้",
    howToUse: {
      title: "แปลง MP3 เป็น VTT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP3",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น VTT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียง MP3 เป็น VTT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลเชิงลึกจากไฟล์ MP3",
      },
      {
        title: "ส่งออกการถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 เป็น TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 เป็น Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 เป็น PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 เป็น SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp3-to-srt": {
    title: "แปลง MP3 เป็น SRT ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องมือแปลง MP3 เป็น SRT ออนไลน์",
    description:
      "UniScribe เป็นบริการถอดความออนไลน์ฟรีที่สามารถแปลงไฟล์ mp3 เป็นรูปแบบซับไตเติ้ล srt ได้อย่างรวดเร็วและแม่นยำ รองรับ 98 ภาษา นอกจากนี้ยังสรุปเนื้อหาและประเด็นสำคัญของไฟล์ของคุณและสร้างแผนผังความคิดเพื่อช่วยให้คุณดึงข้อมูลที่สำคัญออกมา",
    howToUse: {
      title: "แปลง MP3 เป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP3",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียง MP3 เป็น SRT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลเชิงลึกจากไฟล์ MP3",
      },
      {
        title: "ส่งออกการถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 เป็น TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 เป็น Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 เป็น PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 เป็น VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp3-to-txt": {
    title: "แปลง MP3 เป็น TXT ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องแปลง MP3 เป็น TXT ออนไลน์",
    description:
      "ด้วย UniScribe แปลงไฟล์เสียง MP3 เป็นไฟล์ txt ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP3 เป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากเนื้อหาตามเทคโนโลยี AI โดยอัตโนมัติ",
    howToUse: {
      title: "แปลง MP3 เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP3",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียง MP3 เป็น TXT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากไฟล์ MP3",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 เป็น SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "MP3 เป็น Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 เป็น PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 เป็น VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp3-to-word": {
    title: "แปลง MP3 เป็น Word ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องแปลง MP3 เป็น Word ออนไลน์",
    description:
      "แปลงไฟล์เสียง MP3 เป็นเอกสาร Word ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP3 เป็น SRT, TXT, DOCX, PDF, CSV และอื่น ๆ",
    howToUse: {
      title: "แปลง MP3 เป็น DOCX ในสามขั้นตอนง่าย ๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP3",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น DOCX",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียง MP3 เป็นเอกสาร DOCX ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MP3",
      },
      {
        title:
          "ส่งออกผลลัพธ์เป็นข้อความในรูปแบบ DOCX หรือรูปแบบอื่น ๆ (SRT, TXT, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 เป็น SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "MP3 เป็น TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 เป็น PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 เป็น VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
    faqs: [
      {
        question: "จะทำการแปลงเสียง MP3 เป็น DOCX ออนไลน์ฟรีได้อย่างไร?",
        answer:
          "1) อัปโหลดไฟล์ MP3 ของคุณ 2) คลิกปุ่ม 'ลงชื่อเข้าใช้เพื่อถอดความ' 3) รอให้การถอดความเสร็จสิ้น 4) ดาวน์โหลดข้อความของคุณในรูปแบบ DOCX",
      },
      {
        question: "การแปลง MP3 เป็น DOCX มีความแม่นยำแค่ไหน?",
        answer:
          "UniScribe ใช้เทคโนโลยี AI ขั้นสูงเพื่อให้ได้ความแม่นยำสูงในการแปลง MP3 เป็น DOCX ความแม่นยำมักอยู่ในช่วง 90-99% ขึ้นอยู่กับคุณภาพเสียง ความชัดเจนของผู้พูด และเสียงรบกวนพื้นหลัง",
      },
    ],
  },
  "mp3-to-pdf": {
    title: "แปลง MP3 เป็น PDF ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องมือแปลง MP3 เป็น PDF ออนไลน์",
    description:
      "ด้วย UniScribe แปลงไฟล์เสียง MP3 เป็นไฟล์ pdf ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP3 เป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากเนื้อหาตามเทคโนโลยี AI โดยอัตโนมัติ",
    howToUse: {
      title: "แปลง MP3 เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP3",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียง MP3 เป็นเอกสาร PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MP3",
      },
      {
        title: "ส่งออกการถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 เป็น SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "MP3 เป็น TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 เป็น Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 เป็น VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "ai-legal-transcription": {
    title: "การถอดความทางกฎหมายด้วย AI | การรายงานศาลมืออาชีพ",
    displayTitle: "การถอดความทางกฎหมายด้วย AI",
    description:
      "บริการถอดความทางกฎหมายเฉพาะทางด้วยเทคโนโลยี AI เหมาะสำหรับกระบวนการศาล การให้การ และเอกสารทางกฎหมาย.",
  },
  "best-transcription-software": {
    title: "ซอฟต์แวร์ถอดความที่ดีที่สุด | โซลูชันที่ขับเคลื่อนด้วย AI",
    displayTitle: "ซอฟต์แวร์ถอดความที่ดีที่สุด",
    description:
      "สัมผัสประสบการณ์ซอฟต์แวร์ถอดความที่ดีที่สุดด้วยเทคโนโลยี AI ขั้นสูง ใช้งานรวดเร็ว แม่นยำ และง่ายต่อการใช้.",
  },
  "audio-to-text-converter": {
    title:
      "เครื่องแปลงเสียงเป็นข้อความออนไลน์ฟรี | UniScribe - รวดเร็วและปลอดภัย",
    displayTitle: "แปลงเสียงเป็นข้อความออนไลน์ฟรี",
    description:
      "แปลงไฟล์เสียงเป็นข้อความด้วยความแม่นยำระดับมืออาชีพ อัปโหลดไฟล์เสียงและวิดีโอจากอุปกรณ์ในเครื่องของคุณ คลิก 'ถอดความ' เพื่อแปลงเสียงและวิดีโอของคุณเป็นข้อความในไม่กี่วินาที ส่งออกข้อความที่ถอดความในรูปแบบต่างๆ หรือแชร์ลิงก์เพื่อดูโดยตรง",
  },
  "ai-audio-transcription": {
    title: "การถอดเสียงด้วย AI | โซลูชันการแปลงที่ชาญฉลาด",
    displayTitle: "การถอดเสียงด้วย AI",
    description:
      "บริการถอดเสียงด้วย AI ที่ทันสมัย มีความแม่นยำสูงด้วยเทคโนโลยีการประมวลผลภาษาที่ชาญฉลาด.",
  },
  "ai-medical-transcription": {
    title: "การถอดความทางการแพทย์ด้วย AI | เอกสารทางการแพทย์",
    displayTitle: "การถอดความทางการแพทย์ด้วย AI",
    description:
      "บริการถอดความทางการแพทย์ด้วย AI ที่เชี่ยวชาญ ปฏิบัติตาม HIPAA พร้อมความเชี่ยวชาญด้านคำศัพท์ทางการแพทย์.",
  },
  "youtube-transcript-generator": {
    title: "เครื่องมือสร้างถอดความ YouTube | เครื่องมือสร้างเนื้อหา",
    displayTitle: "เครื่องมือสร้างถอดความ YouTube",
    description:
      "สร้างถอดความที่ถูกต้องสำหรับวิดีโอ YouTube เหมาะสำหรับผู้สร้างเนื้อหาและนักการตลาด.",
  },
  "record-lectures-transcription": {
    title: "แอปถอดเสียงการบรรยาย | เครื่องมือการศึกษา",
    displayTitle: "การถอดเสียงการบรรยาย",
    description:
      "บันทึกและถอดเสียงการบรรยายโดยอัตโนมัติ เหมาะสำหรับนักเรียนและวัตถุประสงค์ทางวิชาการ.",
  },
  "google-speech-to-text": {
    title: "ทางเลือก Google Speech to Text | การรู้จำเสียง",
    displayTitle: "ทางเลือก Google Speech to Text",
    description:
      "ทางเลือกมืออาชีพสำหรับ Google Speech-to-Text ฟีเจอร์ขั้นสูงและความแม่นยำที่ดีกว่า.",
  },
  "ai-meeting-summary": {
    title: "เครื่องสร้างสรุปการประชุมด้วย AI | Smart Minutes",
    displayTitle: "เครื่องสร้างสรุปการประชุมด้วย AI",
    description:
      "สร้างสรุปการประชุมโดยอัตโนมัติด้วย AI เปลี่ยนการบันทึกให้เป็นบันทึกที่สามารถดำเนินการได้",
  },
  "lecture-transcription-app": {
    title: "แอปถอดเสียงการบรรยาย | ผู้ช่วยการศึกษา",
    displayTitle: "แอปถอดเสียงการบรรยาย",
    description:
      "บันทึกและถอดเสียงการบรรยายด้วยการจัดระเบียบที่ชาญฉลาด เหมาะสำหรับนักเรียนและผู้สอน.",
  },
  "business-meeting-transcription": {
    title: "การถอดความการประชุมธุรกิจ | บริการมืออาชีพ",
    displayTitle: "การถอดความการประชุมธุรกิจ",
    description:
      "บริการถอดความมืออาชีพสำหรับการประชุมธุรกิจ บันทึกที่ถูกต้องพร้อมการตอบสนองที่รวดเร็ว.",
  },
  "convert-audio-recording": {
    title: "แปลงการบันทึกเสียงเป็นข้อความ | เครื่องมือที่ง่าย",
    displayTitle: "แปลงการบันทึกเสียง",
    description:
      "แปลงการบันทึกเสียงใด ๆ เป็นข้อความได้อย่างง่ายดาย รองรับหลายรูปแบบเสียงและภาษา.",
  },
  "classroom-recording-transcription": {
    title: "การถอดเสียงการบันทึกในห้องเรียน | เครื่องมือการศึกษา",
    displayTitle: "การถอดเสียงการบันทึกในห้องเรียน",
    description:
      "ถอดเสียงการบันทึกในห้องเรียนโดยอัตโนมัติ เหมาะสำหรับนักเรียน ครู และการเรียนทางไกล.",
  },
  "podcast-to-text-converter": {
    title: "เครื่องแปลงพอดแคสต์เป็นข้อความ | เครื่องมือเนื้อหา",
    displayTitle: "เครื่องแปลงพอดแคสต์เป็นข้อความ",
    description:
      "แปลงตอนพอดแคสต์เป็นข้อความได้อย่างง่ายดาย เหมาะสำหรับการนำเนื้อหากลับมาใช้ใหม่และการเข้าถึง.",
  },
  "student-lecture-notes": {
    title: "บันทึกการบรรยายของนักเรียน | ผู้ช่วยการศึกษาปัญญาประดิษฐ์",
    displayTitle: "บันทึกการบรรยายของนักเรียน",
    description:
      "บันทึกการบรรยายอัตโนมัติสำหรับนักเรียน ไม่พลาดข้อมูลสำคัญในชั้นเรียน.",
  },
  "audio-file-converter": {
    title: "ตัวแปลงไฟล์เสียง | การถอดความข้อความ",
    displayTitle: "ตัวแปลงไฟล์เสียง",
    description:
      "แปลงไฟล์เสียงใด ๆ เป็นรูปแบบข้อความ รองรับหลายรูปแบบเสียงและภาษา.",
  },
  "broadcast-media-transcription": {
    title: "การถอดความสื่อกระจายเสียง | เครื่องมือสื่อ",
    displayTitle: "การถอดความสื่อกระจายเสียง",
    description:
      "การถอดความระดับมืออาชีพสำหรับสื่อกระจายเสียง เหมาะสำหรับทีวี วิทยุ และเนื้อหาดิจิทัล.",
  },
  "academic-seminar-transcription": {
    title: "การถอดความสัมมนาทางวิชาการ | เครื่องมือวิจัย",
    displayTitle: "การถอดความสัมมนาทางวิชาการ",
    description:
      "การถอดความที่ปลอดภัยสำหรับสัมมนาทางวิชาการ เอกสารที่ถูกต้องสำหรับการวิจัยและการวิเคราะห์.",
  },
  "youtube-to-text": {
    title: "เครื่องมือแปลง YouTube เป็นข้อความ | เครื่องมือการถอดเสียงวิดีโอ",
    displayTitle: "เครื่องมือแปลง YouTube เป็นข้อความ",
    description:
      "แปลงวิดีโอ YouTube เป็นข้อความด้วยความแม่นยำระดับมืออาชีพ รวดเร็วและปลอดภัย รองรับ 98 ภาษาและมีรูปแบบการส่งออกที่หลากหลาย รวมถึง txt, docx, pdf, srt และอื่นๆ นอกจากนี้ยังสรุปเนื้อหาและจุดสำคัญของวิดีโอของคุณและสร้างแผนผังความคิดเพื่อช่วยให้คุณดึงข้อมูลที่สำคัญออกมา",
    howToUse: {
      title: "แปลง YouTube เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "ใส่ URL ของ YouTube",
        step2: "ลงชื่อเข้าใช้เพื่อถอดเสียง",
        step3: "ส่งออกเป็นการถอดเสียง",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ YouTube เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลเชิงลึกจากวิดีโอ YouTube",
      },
      {
        title: "ส่งออกการถอดเสียงในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "YouTube เป็น Word",
        link: "/l/youtube-to-word",
      },
      {
        text: "YouTube เป็น TXT",
        link: "/l/youtube-to-txt",
      },
      {
        text: "YouTube เป็น SRT",
        link: "/l/youtube-to-srt",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "เสียงเป็นข้อความ",
        link: "/l/audio-to-text",
      },
    ],
  },
  "youtube-to-word": {
    title: "เครื่องมือแปลง YouTube เป็น Word | เครื่องมือเนื้อหาวิดีโอ",
    displayTitle: "เครื่องมือแปลง YouTube เป็น Word",
    description:
      "แปลงวิดีโอ YouTube เป็นเอกสาร Word ด้วยความแม่นยำระดับมืออาชีพ รวดเร็วและปลอดภัย รองรับ 98 ภาษาและมีรูปแบบการส่งออกที่หลากหลาย นอกจากนี้ยังสรุปเนื้อหาและประเด็นสำคัญของวิดีโอของคุณและสร้างแผนผังความคิดเพื่อช่วยให้คุณดึงข้อมูลที่สำคัญออกมา",
    howToUse: {
      title: "แปลง YouTube เป็น Word ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "ใส่ URL ของ YouTube",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น Word",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ YouTube เป็นเอกสาร Word ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลเชิงลึกจากวิดีโอ YouTube",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "YouTube เป็นข้อความ",
        link: "/l/youtube-to-text",
      },
      {
        text: "YouTube เป็น TXT",
        link: "/l/youtube-to-txt",
      },
      {
        text: "YouTube เป็น SRT",
        link: "/l/youtube-to-srt",
      },
      {
        text: "MP4 เป็น Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "M4A เป็น Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "WAV เป็น Word",
        link: "/l/wav-to-word",
      },
      {
        text: "วิดีโอเป็น Word",
        link: "/l/video-to-word",
      },
      {
        text: "เสียงเป็น Word",
        link: "/l/audio-to-word",
      },
    ],
  },
  "youtube-to-txt": {
    title: "ตัวแปลง YouTube เป็น TXT | การส่งออกข้อความที่ง่าย",
    displayTitle: "ตัวแปลง YouTube เป็น TXT",
    description:
      "แปลงวิดีโอ YouTube เป็นไฟล์ TXT ด้วยความแม่นยำระดับมืออาชีพ รวดเร็วและปลอดภัย รองรับ 98 ภาษาและมีรูปแบบการส่งออกที่หลากหลาย นอกจากนี้ยังสรุปเนื้อหาและจุดสำคัญของวิดีโอของคุณและสร้างแผนผังความคิดเพื่อช่วยให้คุณดึงข้อมูลที่สำคัญออกมา",
    howToUse: {
      title: "แปลง YouTube เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "ป้อน URL ของ YouTube",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ YouTube เป็นไฟล์ TXT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลเชิงลึกจากวิดีโอ YouTube",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "YouTube เป็นข้อความ",
        link: "/l/youtube-to-text",
      },
      {
        text: "YouTube เป็น Word",
        link: "/l/youtube-to-word",
      },
      {
        text: "YouTube เป็น SRT",
        link: "/l/youtube-to-srt",
      },
      {
        text: "MP4 เป็น TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "M4A เป็น TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "WAV เป็น TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "วิดีโอเป็น TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "เสียงเป็น TXT",
        link: "/l/audio-to-txt",
      },
    ],
  },
  "youtube-to-srt": {
    title: "YouTube เป็น SRT | เครื่องสร้างไฟล์คำบรรยาย",
    displayTitle: "ตัวแปลง YouTube เป็น SRT",
    description:
      "สร้างไฟล์คำบรรยาย SRT จากวิดีโอ YouTube ด้วยความแม่นยำระดับมืออาชีพ รวดเร็วและปลอดภัย รองรับ 98 ภาษาและมีรูปแบบการส่งออกที่หลากหลาย นอกจากนี้ยังสรุปเนื้อหาและจุดสำคัญของวิดีโอของคุณและสร้างแผนผังความคิดเพื่อช่วยให้คุณดึงข้อมูลที่สำคัญออกมา",
    howToUse: {
      title: "แปลง YouTube เป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "ใส่ URL ของ YouTube",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ YouTube เป็นคำบรรยาย SRT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลเชิงลึกจากวิดีโอ YouTube",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "YouTube เป็นข้อความ",
        link: "/l/youtube-to-text",
      },
      {
        text: "YouTube เป็น Word",
        link: "/l/youtube-to-word",
      },
      {
        text: "YouTube เป็น TXT",
        link: "/l/youtube-to-txt",
      },
      {
        text: "MP4 เป็น SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "M4A เป็น SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "WAV เป็น SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "วิดีโอเป็น SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "เสียงเป็น SRT",
        link: "/l/audio-to-srt",
      },
    ],
  },
  "mp4-to-text-converter": {
    title: "แปลง MP4 เป็นข้อความฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องมือแปลง MP4 เป็นข้อความออนไลน์",
    description:
      "แปลงวิดีโอ MP4 เป็นข้อความด้วยความแม่นยำสูง รวดเร็วและปลอดภัย รองรับ 98 ภาษาและมีรูปแบบการส่งออกที่หลากหลาย รวมถึง txt, docx, pdf, srt และอื่นๆ",
    howToUse: {
      title: "วิธีแปลง MP4 เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP4",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็นข้อความ",
      },
    },
    features: [
      {
        title: "ถอดความวิดีโอ MP4 เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากไฟล์ MP4",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 เป็น SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 เป็น TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 เป็น PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 เป็น VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 เป็น Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 เป็นถอดความ",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "YouTube เป็นข้อความ",
        link: "/l/youtube-to-text",
      },
      {
        text: "เสียงเป็นข้อความ",
        link: "/l/audio-to-text",
      },
    ],
  },
  "mp4-to-word": {
    title: "แปลง MP4 เป็น Word ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องมือแปลง MP4 เป็น Word ออนไลน์",
    description:
      "ด้วย UniScribe, แปลงวิดีโอ MP4 เป็นเอกสาร Word ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP4 เป็น SRT, TXT, Word, PDF, CSV และอื่น ๆ นอกจากนี้ยังสร้างสรุป, แผนที่ความคิด, และข้อมูลสำคัญจากเนื้อหาตามเทคโนโลยี AI โดยอัตโนมัติ",
    howToUse: {
      title: "แปลง MP4 เป็น Word ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP4",
        step2: "คลิกถอดเสียง",
        step3: "ส่งออกเป็น Word",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ MP4 เป็นเอกสาร Word ได้อย่างรวดเร็วและแม่นยำ",
      },
      {
        title: "สร้างสรุป, แผนที่ความคิด, และข้อมูลสำคัญจากไฟล์ MP4",
      },
      {
        title:
          "ส่งออกเป็นเอกสาร Word หรือรูปแบบอื่น ๆ (SRT, TXT, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 เป็น SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 เป็น TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 เป็น PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 เป็น VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 เป็นถอดเสียง",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-srt": {
    title: "แปลง MP4 เป็น SRT ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องมือแปลง MP4 เป็น SRT ออนไลน์",
    description:
      "ด้วย UniScribe แปลงวิดีโอ MP4 เป็นคำบรรยาย SRT ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP4 เป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากเนื้อหาตามเทคโนโลยี AI โดยอัตโนมัติ",
    howToUse: {
      title: "แปลง MP4 เป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP4",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ MP4 เป็นคำบรรยาย SRT อย่างรวดเร็วและแม่นยำ",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากไฟล์ MP4",
      },
      {
        title:
          "ส่งออกเป็นคำบรรยาย SRT หรือรูปแบบอื่นๆ (TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 เป็น Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 เป็น TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 เป็น PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 เป็น VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 เป็นถอดความ",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-pdf": {
    title: "แปลง MP4 เป็น PDF ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องแปลง MP4 เป็น PDF ออนไลน์",
    description:
      "ด้วย UniScribe แปลงวิดีโอ MP4 เป็นเอกสาร PDF ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP4 เป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากเนื้อหาตามเทคโนโลยี AI โดยอัตโนมัติ",
    howToUse: {
      title: "แปลง MP4 เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP4",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ MP4 เป็นเอกสาร PDF อย่างรวดเร็วและแม่นยำ",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากไฟล์ MP4",
      },
      {
        title:
          "ส่งออกเป็นเอกสาร PDF หรือรูปแบบอื่นๆ (SRT, TXT, Word, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 เป็น SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 เป็น TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 เป็น Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 เป็น VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 เป็นถอดความ",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-vtt": {
    title: "แปลง MP4 เป็น VTT ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องแปลง MP4 เป็น VTT ออนไลน์",
    description:
      "ด้วย UniScribe แปลงวิดีโอ MP4 เป็นซับไตเติ้ล VTT ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP4 เป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากเนื้อหาโดยอิงจากเทคโนโลยี AI อัตโนมัติ",
    howToUse: {
      title: "แปลง MP4 เป็น VTT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP4",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น VTT",
      },
    },
    features: [
      {
        title: "ถอดความวิดีโอ MP4 เป็นซับไตเติ้ล VTT อย่างรวดเร็วและแม่นยำ",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MP4",
      },
      {
        title:
          "ส่งออกเป็นซับไตเติ้ล VTT หรือรูปแบบอื่นๆ (SRT, TXT, Word, PDF, CSV)",
      },
    ],
    converters: [
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 เป็น SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 เป็น TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 เป็น Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 เป็น PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 เป็นถอดความ",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-txt": {
    title: "แปลง MP4 เป็น TXT ฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องมือแปลง MP4 เป็น TXT ออนไลน์",
    description:
      "ด้วย UniScribe, แปลงวิดีโอ MP4 เป็นไฟล์ TXT ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP4 เป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป, แผนที่ความคิด, และข้อมูลสำคัญจากเนื้อหาตามเทคโนโลยี AI โดยอัตโนมัติ",
    howToUse: {
      title: "แปลง MP4 เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP4",
        step2: "คลิกแปลง",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ MP4 เป็นไฟล์ TXT ได้อย่างรวดเร็วและแม่นยำ",
      },
      {
        title: "สร้างสรุป, แผนที่ความคิด, และข้อมูลสำคัญจากไฟล์ MP4",
      },
      {
        title: "ส่งออกเป็นไฟล์ TXT หรือรูปแบบอื่นๆ (SRT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 เป็น SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 เป็น Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 เป็น PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 เป็น VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 เป็นการถอดความ",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-transcript": {
    title: "แปลง MP4 เป็นข้อความฟรีออนไลน์ - ข powered by AI",
    displayTitle: "เครื่องแปลง MP4 เป็นข้อความออนไลน์",
    description:
      "ด้วย UniScribe แปลงวิดีโอ MP4 เป็นข้อความในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์ MP4 เป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนผังความคิด และข้อมูลเชิงลึกจากเนื้อหาตามเทคโนโลยี AI โดยอัตโนมัติ",
    howToUse: {
      title: "แปลง MP4 เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MP4",
        step2: "คลิกแปลง",
        step3: "ส่งออกเป็นข้อความ",
      },
    },
    features: [
      {
        title: "แปลงวิดีโอ MP4 เป็นข้อความอย่างรวดเร็วและแม่นยำ",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลเชิงลึกจากไฟล์ MP4",
      },
      {
        title: "ส่งออกเป็นข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 เป็น SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 เป็น Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 เป็น PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 เป็น VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "wav-to-text": {
    title: "แปลง WAV เป็นข้อความออนไลน์ฟรี – ถอดความด้วย AI",
    displayTitle: "ตัวแปลง WAV เป็นข้อความออนไลน์",
    description:
      "ใช้ UniScribe เพื่อแปลงไฟล์ WAV เป็นข้อความฟรีออนไลน์ ถอดความไฟล์ WAV เป็น TXT ในไม่กี่วินาที รองรับ 98 ภาษา",
    howToUse: {
      title: "วิธีแปลง WAV เป็นข้อความในสามขั้นตอน?",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WAV",
        step2: "คลิกถอดความ",
        step3: "ส่งออกผลลัพธ์ข้อความ",
      },
    },
    features: [
      {
        title: "แปลง WAV และรูปแบบไฟล์อื่นเป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ WAV",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV เป็น SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV เป็น VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV เป็น PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV เป็น Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV เป็น TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV เป็นถอดความ",
        link: "/l/wav-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
    ],
    faqs: [
      {
        question: "ฉันจะแปลงไฟล์ WAV เป็นข้อความได้อย่างไร?",
        answer:
          "1. อัปโหลดไฟล์ WAV ของคุณและรอให้เสร็จสิ้น การเลือกภาษาที่ถูกต้องจะนำไปสู่ผลลัพธ์การถอดความที่ดีกว่า 2. ลงชื่อเข้าใช้และเริ่มถอดความ 3. ส่งออกถอดความในรูปแบบที่คุณต้องการ.",
      },
      {
        question: "ใช้เวลานานแค่ไหนในการแปลง WAV เป็นข้อความ?",
        answer:
          "จากการทดสอบของเรา เมื่อไฟล์ WAV ถูกอัปโหลด UniScribe สามารถถอดความไฟล์ WAV ขนาด 1 ชั่วโมงได้ภายในเวลาน้อยกว่าหนึ่งนาที มันเป็นหนึ่งในเครื่องมือถอดความที่เร็วที่สุดที่มีอยู่ อย่างไรก็ตาม เวลาถอดความสุดท้ายขึ้นอยู่กับความยาวจริงของไฟล์.",
      },
      {
        question: "การแปลง WAV เป็นข้อความมีความแม่นยำแค่ไหน?",
        answer:
          "เราใช้โมเดล Whisper AI ที่ปรับแต่งเพื่อถอดความเสียงอย่างแม่นยำในถึง 98 ภาษา—แม้จะมีสำเนียง! อย่างไรก็ตาม คุณภาพการถอดความยังขึ้นอยู่กับเสียงเอง สำหรับผลลัพธ์ที่ดีที่สุด ให้พยายามลดเสียงรบกวนในพื้นหลังและมั่นใจว่ามีคนพูดเพียงคนเดียวในแต่ละครั้ง.",
      },
      {
        question: "UniScribe รองรับรูปแบบใดบ้างสำหรับการส่งออกถอดความ?",
        answer:
          "คุณสามารถส่งออกถอดความใน 6 รูปแบบ: SRT, TXT, Word, PDF, CSV, และ VTT.",
      },
    ],
  },
  "wav-to-word": {
    title:
      "เครื่องมือแปลง WAV เป็น Word ออนไลน์ฟรี | ถอดเสียง WAV เป็น Word โดย AI",
    displayTitle: "เครื่องมือแปลง WAV เป็น Word ออนไลน์",
    description:
      "ใช้ UniScribe เพื่อแปลงไฟล์ WAV เป็น Word ด้วยความแม่นยำสูงที่ขับเคลื่อนโดย AI ถอดเสียงไฟล์ WAV เป็น Word ในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง WAV เป็น Word ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WAV",
        step2: "คลิกถอดเสียง",
        step3: "ส่งออกเป็น Word",
      },
    },
    features: [
      {
        title: "แปลง WAV และรูปแบบไฟล์อื่นๆ เป็นเอกสาร Word ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ WAV",
      },
      {
        title: "ส่งออกถอดเสียงในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV เป็น SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV เป็น VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV เป็น PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV เป็น TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV เป็นถอดเสียง",
        link: "/l/wav-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-srt": {
    title: "เครื่องแปลง WAV เป็น SRT ออนไลน์ฟรี | ถอดความ WAV เป็น SRT",
    displayTitle: "เครื่องแปลง WAV เป็น SRT ออนไลน์",
    description:
      "ใช้ UniScribe เพื่อแปลงไฟล์ WAV เป็น SRT ด้วยความแม่นยำสูงที่ขับเคลื่อนด้วย AI ถอดความไฟล์ WAV เป็น SRT ในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง WAV เป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WAV",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลง WAV และรูปแบบไฟล์อื่นๆ เป็น SRT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากไฟล์ WAV",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV เป็น Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV เป็น VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV เป็น PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV เป็น TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV เป็นถอดความ",
        link: "/l/wav-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-vtt": {
    title: "เครื่องแปลง WAV เป็น VTT ออนไลน์ฟรี | ถอดเสียง WAV เป็น VTT",
    displayTitle: "เครื่องแปลง WAV เป็น VTT ออนไลน์",
    description: "สร้างคำบรรยาย VTT จากเสียง WAV เหมาะสำหรับเนื้อหาเสียงบนเว็บ",
    howToUse: {
      title: "แปลง WAV เป็น VTT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WAV",
        step2: "คลิกถอดเสียง",
        step3: "ส่งออกเป็น VTT",
      },
    },
    features: [
      {
        title: "แปลง WAV และรูปแบบไฟล์อื่นๆ เป็น VTT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ WAV",
      },
      {
        title: "ส่งออกถอดเสียงในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV เป็น SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV เป็น Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV เป็น PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV เป็น TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV เป็นถอดเสียง",
        link: "/l/wav-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-pdf": {
    title: "เครื่องแปลง WAV เป็น PDF ออนไลน์ฟรี | ถอดความ WAV เป็น PDF",
    displayTitle: "เครื่องแปลง WAV เป็น PDF ออนไลน์",
    description:
      "ใช้ UniScribe เพื่อแปลงไฟล์ WAV เป็น PDF ด้วยความแม่นยำสูงที่ขับเคลื่อนโดย AI ถอดความไฟล์ WAV เป็น PDF ในไม่กี่วินาที รองรับ 98 ภาษา มันจะสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง WAV เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WAV",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลง WAV และรูปแบบไฟล์อื่นๆ เป็น PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากไฟล์ WAV",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV เป็น SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV เป็น Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV เป็น VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV เป็น TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "WAV เป็นถอดความ",
        link: "/l/wav-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-transcript": {
    title: "เครื่องมือแปลง WAV เป็นข้อความออนไลน์ฟรี | แปลง WAV เป็นข้อความ",
    displayTitle: "เครื่องมือแปลง WAV เป็นข้อความออนไลน์",
    description:
      "ใช้ UniScribe เพื่อแปลงไฟล์ WAV เป็นข้อความด้วยความแม่นยำสูงที่ขับเคลื่อนโดย AI แปลงไฟล์ WAV เป็นข้อความในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง WAV เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WAV",
        step2: "คลิกแปลง",
        step3: "ส่งออกข้อความ",
      },
    },
    features: [
      {
        title: "แปลง WAV และรูปแบบไฟล์อื่นๆ เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากไฟล์ WAV",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV เป็น SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV เป็น Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV เป็น VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV เป็น PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV เป็น TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-txt": {
    title: "เครื่องแปลง WAV เป็น TXT ออนไลน์ฟรี | ถอดเสียง WAV เป็น TXT",
    displayTitle: "เครื่องแปลง WAV เป็น TXT ออนไลน์",
    description:
      "ใช้ UniScribe เพื่อแปลงไฟล์ WAV เป็น TXT ด้วยความแม่นยำสูงที่ขับเคลื่อนโดย AI ถอดเสียงไฟล์ WAV เป็น TXT ในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง WAV เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WAV",
        step2: "คลิกถอดเสียง",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลง WAV และรูปแบบไฟล์อื่นๆ เป็น TXT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากไฟล์ WAV",
      },
      {
        title: "ส่งออกการถอดเสียงในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV เป็น Text",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV เป็น SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV เป็น Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV เป็น VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV เป็น PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV เป็น Transcript",
        link: "/l/wav-to-transcript",
      },
      {
        text: "MP3 เป็น Text",
        link: "/l/mp3-to-text",
      },
      {
        text: "M4A เป็น Text",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "m4a-to-text": {
    title: "แปลง M4A เป็นข้อความออนไลน์ฟรี | ถอดความ M4A เป็นข้อความ",
    displayTitle: "เครื่องแปลง M4A เป็นข้อความออนไลน์",
    description:
      "จะเปลี่ยนไฟล์ M4A ขนาด 1 ชั่วโมงให้เป็นข้อความในเวลาเพียง 1 นาทีได้อย่างไร? ด้วย UniScribe มันง่ายเหมือน 1-2-3: อัปโหลด -> ถอดความ -> ส่งออก รองรับ 98 ภาษาและสามารถแปลงไฟล์ M4A เป็น SRT, TXT, Word, PDF, CSV และ VTT นอกจากนี้ยังสร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง M4A เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ M4A",
        step2: "คลิกถอดความ",
        step3: "ส่งออกผลลัพธ์เป็นข้อความ",
      },
    },
    features: [
      {
        title: "แปลง M4A และรูปแบบไฟล์อื่นๆ เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ M4A",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A เป็น Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A เป็น PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A เป็น SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A เป็น VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A เป็นถอดความ",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "M4A เป็น TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-txt": {
    title: "แปลง M4A เป็น TXT ออนไลน์ฟรี | M4A เป็นข้อความ",
    displayTitle: "เครื่องแปลง M4A เป็น TXT ออนไลน์",
    description:
      "จะเปลี่ยนไฟล์ M4A ขนาด 1 ชั่วโมงให้เป็น txt ในเวลาเพียง 1 นาทีได้อย่างไร? ใช้ UniScribe เพื่อแปลงไฟล์เสียง M4A เป็น txt ด้วยความแม่นยำสูงที่ขับเคลื่อนโดย AI ถอดเสียงไฟล์ M4A เป็น txt ในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง M4A เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ M4A",
        step2: "คลิกถอดเสียง",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลง M4A และรูปแบบไฟล์อื่นๆ เป็น TXT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ M4A",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A เป็น Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A เป็น PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A เป็น SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A เป็น VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A เป็นถอดความ",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-word": {
    title: "แปลง M4A เป็น Word ออนไลน์ฟรี | M4A เป็นข้อความ - ข powered by AI",
    displayTitle: "เครื่องแปลง M4A เป็น Word ออนไลน์",
    description:
      "จะเปลี่ยนไฟล์ M4A ขนาด 1 ชั่วโมงเป็น Word ในเวลาเพียง 1 นาทีได้อย่างไร? ใช้ UniScribe เพื่อแปลงไฟล์เสียง M4A เป็น Word ด้วยความแม่นยำสูงที่ข powered by AI ถอดความไฟล์ M4A เป็น Word ในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง M4A เป็น Word ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ M4A",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น Word",
      },
    },
    features: [
      {
        title: "แปลง M4A และรูปแบบไฟล์อื่นๆ เป็นเอกสาร Word ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากไฟล์ M4A",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A เป็น TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A เป็น PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A เป็น SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A เป็น VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A เป็นถอดความ",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-pdf": {
    title: "แปลง M4A เป็น PDF ออนไลน์ฟรี | M4A เป็นข้อความ - ข powered by AI",
    displayTitle: "เครื่องแปลง M4A เป็น PDF ออนไลน์",
    description:
      "จะเปลี่ยนไฟล์ M4A ขนาด 1 ชั่วโมงเป็น PDF ในเวลาเพียง 1 นาทีได้อย่างไร? ใช้ UniScribe เพื่อแปลงไฟล์เสียง M4A เป็น PDF ด้วยความแม่นยำสูงที่ข powered by AI ถอดความไฟล์ M4A เป็น PDF ในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง M4A เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ M4A",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลง M4A และรูปแบบไฟล์อื่นๆ เป็น PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากไฟล์ M4A",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A เป็น TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A เป็น Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A เป็น SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A เป็น VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A เป็นถอดความ",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-srt": {
    title: "แปลง M4A เป็น SRT ออนไลน์ฟรี | M4A เป็นข้อความ - ข powered by AI",
    displayTitle: "เครื่องแปลง M4A เป็น SRT ออนไลน์",
    description:
      "จะเปลี่ยนไฟล์ M4A ขนาด 1 ชั่วโมงเป็น SRT ในเวลาเพียง 1 นาทีได้อย่างไร? ใช้ UniScribe เพื่อแปลงไฟล์เสียง M4A เป็น SRT ด้วยความแม่นยำสูงที่ข powered by AI ถอดความไฟล์ M4A เป็น SRT ในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนผังความคิด และข้อมูลสำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง M4A เป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ M4A",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลง M4A และรูปแบบไฟล์อื่นๆ เป็น SRT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลสำคัญจากไฟล์ M4A",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A เป็น TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A เป็น Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A เป็น PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A เป็น VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A เป็นถอดความ",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-vtt": {
    title: "แปลง M4A เป็น VTT ออนไลน์ฟรี | M4A เป็นข้อความ - ข powered by AI",
    displayTitle: "เครื่องแปลง M4A เป็น VTT ออนไลน์",
    description:
      "จะเปลี่ยนไฟล์ M4A ขนาด 1 ชั่วโมงให้เป็น VTT ในเวลาเพียง 1 นาทีได้อย่างไร? ใช้ UniScribe เพื่อแปลงไฟล์เสียง M4A เป็น VTT ด้วยความแม่นยำสูงที่ข powered by AI ถอดความไฟล์ M4A เป็น VTT ในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง M4A เป็น VTT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ M4A",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น VTT",
      },
    },
    features: [
      {
        title: "แปลง M4A และรูปแบบไฟล์อื่นๆ เป็น VTT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ M4A",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A เป็น TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A เป็น Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A เป็น PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A เป็น SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A เป็นถอดความ",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-transcript": {
    title: "แปลง M4A เป็นข้อความออนไลน์ฟรี | M4A เป็นข้อความ - ข powered by AI",
    displayTitle: "เครื่องแปลง M4A เป็นข้อความออนไลน์",
    description:
      "จะทำอย่างไรให้ไฟล์ M4A ขนาด 1 ชั่วโมงกลายเป็นข้อความในเวลาเพียง 1 นาที? ใช้ UniScribe เพื่อแปลงไฟล์เสียง M4A เป็นข้อความด้วยความแม่นยำสูงที่ข powered by AI แปลงไฟล์ M4A เป็นข้อความในไม่กี่วินาที รองรับ 98 ภาษา มันสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากเนื้อหาโดยอัตโนมัติ",
    howToUse: {
      title: "แปลง M4A เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ M4A",
        step2: "คลิกแปลง",
        step3: "ส่งออกข้อความ",
      },
    },
    features: [
      {
        title: "แปลง M4A และรูปแบบไฟล์อื่นๆ เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากไฟล์ M4A",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A เป็น TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A เป็น Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A เป็น PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A เป็น SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A เป็น VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "transcribe-mp4": {
    title: "ถอดความ MP4 | การถอดความวิดีโอที่แม่นยำ",
    displayTitle: "ถอดความ MP4",
    description:
      "ถอดความวิดีโอ MP4 เป็นข้อความได้อย่างง่ายดาย เหมาะสำหรับผู้สร้างเนื้อหาและนักการศึกษา.",
  },
  "video-to-text": {
    title: "แปลงวิดีโอเป็นข้อความในไม่กี่วินาทีฟรี",
    displayTitle: "เครื่องแปลงวิดีโอเป็นข้อความที่ขับเคลื่อนด้วย AI",
    description:
      "เปลี่ยนไฟล์วิดีโอ 1 ชั่วโมงเป็นไฟล์ข้อความในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลงวิดีโอเป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์วิดีโอ",
        step2: "คลิกถอดความ",
        step3: "ส่งออกถอดความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์วิดีโอเป็นข้อความในไม่กี่วินาทีด้วย AI",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์วิดีโอ",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "วิดีโอเป็น TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "วิดีโอเป็น SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "วิดีโอเป็น Word",
        link: "/l/video-to-word",
      },
      {
        text: "วิดีโอเป็น PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "วิดีโอเป็น VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "วิดีโอเป็นถอดความ",
        link: "/l/video-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-txt": {
    title: "แปลงวิดีโอเป็น TXT ในไม่กี่วินาทีฟรี",
    displayTitle: "เครื่องแปลงวิดีโอเป็น TXT ที่ขับเคลื่อนด้วย AI",
    description:
      "เปลี่ยนไฟล์วิดีโอ 1 ชั่วโมงเป็นไฟล์ txt ในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลงวิดีโอเป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์วิดีโอ",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์วิดีโอเป็นเอกสาร TXT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์วิดีโอ",
      },
      {
        title: "ส่งออกการถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "วิดีโอเป็น SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "วิดีโอเป็น Word",
        link: "/l/video-to-word",
      },
      {
        text: "วิดีโอเป็น PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "วิดีโอเป็น VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "วิดีโอเป็นการถอดความ",
        link: "/l/video-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-srt": {
    title: "แปลงวิดีโอเป็น SRT ในไม่กี่วินาทีฟรี",
    displayTitle: "เครื่องแปลงวิดีโอเป็น SRT ที่ขับเคลื่อนด้วย AI",
    description:
      "เปลี่ยนไฟล์วิดีโอ 1 ชั่วโมงเป็นไฟล์คำบรรยาย srt ในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลงวิดีโอเป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์วิดีโอ",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์วิดีโอเป็นคำบรรยาย SRT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์วิดีโอ",
      },
      {
        title: "ส่งออกการถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "วิดีโอเป็น TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "วิดีโอเป็น Word",
        link: "/l/video-to-word",
      },
      {
        text: "วิดีโอเป็น PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "วิดีโอเป็น VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "วิดีโอเป็นการถอดความ",
        link: "/l/video-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-vtt": {
    title: "แปลงวิดีโอเป็น VTT ในไม่กี่วินาทีฟรี",
    displayTitle: "เครื่องแปลงวิดีโอเป็น VTT ที่ขับเคลื่อนด้วย AI",
    description:
      "เปลี่ยนไฟล์วิดีโอ 1 ชั่วโมงให้เป็นไฟล์คำบรรยาย vtt ในเวลาไม่ถึง 1 นาที การสนับสนุนหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลงวิดีโอเป็น VTT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์วิดีโอ",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น VTT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์วิดีโอเป็นคำบรรยาย VTT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์วิดีโอ",
      },
      {
        title: "ส่งออกการถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "วิดีโอเป็น TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "วิดีโอเป็น SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "วิดีโอเป็น Word",
        link: "/l/video-to-word",
      },
      {
        text: "วิดีโอเป็น PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "วิดีโอเป็นการถอดความ",
        link: "/l/video-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-pdf": {
    title: "แปลงวิดีโอเป็น PDF ในไม่กี่วินาทีฟรี",
    displayTitle: "เครื่องแปลงวิดีโอเป็น PDF โดย AI",
    description:
      "ด้วย UniScribe แปลงไฟล์วิดีโอเป็นเอกสาร PDF ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์วิดีโอเป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนผังความคิด และข้อมูลสำคัญจากเนื้อหาตามเทคโนโลยี AI โดยอัตโนมัติ",
    howToUse: {
      title: "แปลงวิดีโอเป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์วิดีโอ",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลงไฟล์วิดีโอเป็นเอกสาร PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลสำคัญจากไฟล์วิดีโอ",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "วิดีโอเป็น TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "วิดีโอเป็น SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "วิดีโอเป็น Word",
        link: "/l/video-to-word",
      },
      {
        text: "วิดีโอเป็น VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "วิดีโอเป็นถอดความ",
        link: "/l/video-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-word": {
    title: "แปลงวิดีโอเป็นเอกสาร Word ในไม่กี่วินาทีฟรี",
    displayTitle: "เครื่องแปลงวิดีโอเป็น Word ที่ขับเคลื่อนโดย AI",
    description:
      "แปลงไฟล์วิดีโอ 1 ชั่วโมงเป็นเอกสาร Word ในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลงวิดีโอเป็น Word ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์วิดีโอ",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น Word",
      },
    },
    features: [
      {
        title: "แปลงไฟล์วิดีโอเป็นเอกสาร Word ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์วิดีโอ",
      },
      {
        title: "ส่งออกการถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "วิดีโอเป็น TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "วิดีโอเป็น SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "วิดีโอเป็น PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "วิดีโอเป็น VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "วิดีโอเป็นการถอดความ",
        link: "/l/video-to-transcript",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-transcript": {
    title: "แปลงวิดีโอเป็นข้อความฟรีออนไลน์",
    displayTitle: "เครื่องแปลงวิดีโอเป็นข้อความออนไลน์",
    description:
      "ด้วย UniScribe คุณสามารถแปลงไฟล์วิดีโอให้เป็นข้อความที่ถูกต้องในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์วิดีโอเป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนผังความคิด และข้อมูลสำคัญจากเนื้อหาโดยอิงจากเทคโนโลยี AI อัตโนมัติ",
    howToUse: {
      title: "แปลงวิดีโอเป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์วิดีโอ",
        step2: "คลิกแปลง",
        step3: "ส่งออกข้อความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์วิดีโอเป็นข้อความที่ถูกต้องในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนผังความคิด และข้อมูลสำคัญจากไฟล์วิดีโอ",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "วิดีโอเป็น TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "วิดีโอเป็น SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "วิดีโอเป็น Word",
        link: "/l/video-to-word",
      },
      {
        text: "วิดีโอเป็น PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "วิดีโอเป็น VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "audio-to-text": {
    title: "แปลงเสียงเป็นข้อความฟรีออนไลน์ | รวดเร็วและแม่นยำ",
    displayTitle: "เครื่องแปลงเสียงเป็นข้อความที่ขับเคลื่อนโดย AI",
    description:
      "แปลงไฟล์เสียง 1 ชั่วโมงเป็นข้อความในเวลาไม่ถึง 1 นาที UniScribe รองรับหลายรูปแบบเสียงและมีตัวเลือกการส่งออกที่หลากหลายเพื่อความสะดวกของคุณ.",
    howToUse: {
      title: "แปลงเสียงเป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์เสียง",
        step2: "คลิกถอดความ",
        step3: "ส่งออกถอดความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียงเป็นข้อความในเวลาอันสั้น",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์เสียง",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "เสียงเป็น TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "เสียงเป็น SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "เสียงเป็น Word",
        link: "/l/audio-to-word",
      },
      {
        text: "เสียงเป็น PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "เสียงเป็น VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "YouTube เป็นข้อความ",
        link: "/l/youtube-to-text",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
    ],
  },
  "audio-to-txt": {
    title: "แปลงเสียงเป็น TXT ฟรีออนไลน์ - รวดเร็วและแม่นยำ",
    displayTitle: "เครื่องแปลงเสียงเป็น TXT ที่ขับเคลื่อนโดย AI",
    description:
      "ด้วย UniScribe เปลี่ยนไฟล์เสียง 1 ชั่วโมงให้เป็นไฟล์ txt ในเวลาไม่ถึง 1 นาที รองรับ 98 ภาษาและสามารถแปลงไฟล์เสียงเป็น SRT, TXT, Word, PDF, CSV และอื่นๆ",
    howToUse: {
      title: "แปลงเสียงเป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์เสียง",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียงเป็นไฟล์ TXT ในเวลาอันสั้น",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์เสียง",
      },
      {
        title:
          "ส่งออกเป็น TXT หรือรูปแบบการถอดความอื่นๆ (SRT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "เสียงเป็นข้อความ",
        link: "/l/audio-to-text",
      },
      {
        text: "เสียงเป็น SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "เสียงเป็น Word",
        link: "/l/audio-to-word",
      },
      {
        text: "เสียงเป็น PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "เสียงเป็น VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "audio-to-word": {
    title: "แปลงเสียงเป็นเอกสาร Word ฟรีออนไลน์",
    displayTitle: "เครื่องแปลงเสียงเป็นเอกสาร Word โดยใช้ AI",
    description:
      "ด้วย UniScribe คุณสามารถถอดเสียงไฟล์เสียง 1 ชั่วโมงเป็นเอกสาร Word ในเวลาไม่ถึง 1 นาที รองรับ 98 ภาษาและสามารถแปลงไฟล์เสียงเป็น SRT, TXT, Word, PDF, CSV และอื่นๆ",
    howToUse: {
      title: "แปลงเสียงเป็นเอกสาร Word ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์เสียง",
        step2: "คลิกถอดเสียง",
        step3: "ส่งออกเป็น Word",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียงเป็นเอกสาร Word ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์เสียง",
      },
      {
        title:
          "ส่งออกข้อความถอดเสียงในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "เสียงเป็นข้อความ",
        link: "/l/audio-to-text",
      },
      {
        text: "เสียงเป็น SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "เสียงเป็น TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "เสียงเป็น PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "เสียงเป็น VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "audio-to-pdf": {
    title: "แปลงเสียงเป็น PDF ฟรีออนไลน์",
    displayTitle: "เครื่องแปลงเสียงเป็น PDF ที่ขับเคลื่อนด้วย AI",
    description:
      "ด้วย UniScribe แปลงไฟล์เสียง 1 ชั่วโมงเป็นเอกสาร pdf ในเวลาไม่ถึง 1 นาที รองรับ 98 ภาษาและสามารถแปลงไฟล์เสียงเป็น SRT, TXT, Word, PDF, CSV และอื่นๆ",
    howToUse: {
      title: "แปลงเสียงเป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์เสียง",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียงเป็นเอกสาร PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์เสียง",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "เสียงเป็นข้อความ",
        link: "/l/audio-to-text",
      },
      {
        text: "เสียงเป็น SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "เสียงเป็น TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "เสียงเป็น Word",
        link: "/l/audio-to-word",
      },
      {
        text: "เสียงเป็น VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "วิดีโอเป็นข้อความ",
        link: "/l/video-to-text",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "audio-to-vtt": {
    title: "แปลงเสียงเป็น VTT ฟรีออนไลน์",
    displayTitle: "เครื่องแปลงเสียงเป็น VTT ที่ขับเคลื่อนด้วย AI",
    description:
      "ด้วย UniScribe แปลงไฟล์เสียงเป็นรูปแบบซับไตเติ้ล VTT ในไม่กี่นาทีฟรีออนไลน์ รองรับ 98 ภาษาและสามารถแปลงไฟล์เสียงเป็น SRT, TXT, Word, PDF, CSV และอื่นๆ นอกจากนี้ยังสร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากเนื้อหาโดยอิงจากเทคโนโลยี AI อัตโนมัติ",
    howToUse: {
      title: "แปลงเสียงเป็น VTT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์เสียง",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น VTT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียงเป็นซับไตเติ้ล VTT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกที่สำคัญจากไฟล์เสียง",
      },
      {
        title: "ส่งออกถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "เสียงเป็นข้อความ",
        link: "/l/audio-to-text",
      },
      {
        text: "เสียงเป็น SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "เสียงเป็น TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "เสียงเป็น Word",
        link: "/l/audio-to-word",
      },
      {
        text: "เสียงเป็น PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "audio-to-srt": {
    title: "แปลงเสียงเป็น SRT ฟรีออนไลน์",
    displayTitle: "เครื่องแปลงเสียงเป็น SRT ที่ขับเคลื่อนด้วย AI",
    description:
      "ด้วย UniScribe แปลงไฟล์เสียง 1 ชั่วโมงเป็นไฟล์คำบรรยาย srt ในเวลาไม่ถึง 1 นาที รองรับ 98 ภาษาและสามารถแปลงไฟล์เสียงเป็น SRT, TXT, Word, PDF, CSV และอื่นๆ",
    howToUse: {
      title: "แปลงเสียงเป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์เสียง",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์เสียงเป็นคำบรรยาย SRT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์เสียง",
      },
      {
        title:
          "ส่งออกเป็น SRT หรือรูปแบบการถอดความอื่นๆ (SRT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "เสียงเป็นข้อความ",
        link: "/l/audio-to-text",
      },
      {
        text: "เสียงเป็น Word",
        link: "/l/audio-to-word",
      },
      {
        text: "เสียงเป็น PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "เสียงเป็น TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "เสียงเป็น VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
    ],
  },
  "webm-to-text": {
    title: "แปลง WebM เป็นข้อความในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง WebM เป็นข้อความ",
    description:
      "แปลงไฟล์ WebM ขนาด 1 ชั่วโมงเป็นข้อความในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง WebM เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WebM",
        step2: "คลิกถอดความ",
        step3: "ส่งออกผลลัพธ์ข้อความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ WebM เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ WebM",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WebM เป็น TXT",
        link: "/l/webm-to-txt",
      },
      {
        text: "WebM เป็น PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG เป็นข้อความ",
        link: "/l/ogg-to-text",
      },
      {
        text: "OPUS เป็นข้อความ",
        link: "/l/opus-to-text",
      },
      {
        text: "AAC เป็นข้อความ",
        link: "/l/aac-to-text",
      },
    ],
  },
  "webm-to-pdf": {
    title: "แปลง WebM เป็น PDF ในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง WebM เป็น PDF",
    description:
      "แปลงไฟล์ WebM ขนาด 1 ชั่วโมงเป็นเอกสาร PDF ในเวลาไม่ถึง 1 นาทีด้วยพลังของ AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง WebM เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WebM",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ WebM เป็นเอกสาร PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ WebM",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "WebM เป็น TXT",
        link: "/l/webm-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG เป็น PDF",
        link: "/l/ogg-to-pdf",
      },
      {
        text: "OPUS เป็น PDF",
        link: "/l/opus-to-pdf",
      },
      {
        text: "MOV เป็น PDF",
        link: "/l/mov-to-pdf",
      },
    ],
  },
  "mpeg-to-text": {
    title: "แปลง MPEG เป็นข้อความในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง MPEG เป็นข้อความ",
    description:
      "แปลงไฟล์ MPEG ขนาด 1 ชั่วโมงเป็นข้อความในเวลาไม่ถึง 1 นาทีด้วยพลังของ AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง MPEG เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MPEG",
        step2: "คลิกถอดความ",
        step3: "ส่งออกข้อความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ MPEG เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MPEG",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MPEG เป็น Word",
        link: "/l/mpeg-to-word",
      },
      {
        text: "MPEG เป็น PDF",
        link: "/l/mpeg-to-pdf",
      },
      {
        text: "MPEG เป็น TXT",
        link: "/l/mpeg-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "MOV เป็นข้อความ",
        link: "/l/mov-to-text",
      },
    ],
  },
  "mpeg-to-word": {
    title: "แปลง MPEG เป็น Word ในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง MPEG เป็น Word",
    description:
      "แปลงไฟล์ MPEG ขนาด 1 ชั่วโมงเป็นเอกสาร Word ในเวลาไม่ถึง 1 นาทีด้วยพลังของ AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง MPEG เป็น Word ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MPEG",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น Word",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ MPEG เป็นเอกสาร Word ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MPEG",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MPEG เป็นข้อความ",
        link: "/l/mpeg-to-text",
      },
      {
        text: "MPEG เป็น PDF",
        link: "/l/mpeg-to-pdf",
      },
      {
        text: "MPEG เป็น TXT",
        link: "/l/mpeg-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "MOV เป็นข้อความ",
        link: "/l/mov-to-text",
      },
    ],
  },
  "mpeg-to-pdf": {
    title: "แปลง MPEG เป็น PDF ออนไลน์ฟรี",
    displayTitle: "ตัวแปลง MPEG เป็น PDF",
    description:
      "แปลงไฟล์ MPEG ขนาด 1 ชั่วโมงเป็นเอกสาร PDF ในเวลาไม่ถึง 1 นาทีด้วย AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง MPEG เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MPEG",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ MPEG เป็นเอกสาร PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MPEG",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MPEG เป็นข้อความ",
        link: "/l/mpeg-to-text",
      },
      {
        text: "MPEG เป็น Word",
        link: "/l/mpeg-to-word",
      },
      {
        text: "MPEG เป็น TXT",
        link: "/l/mpeg-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM เป็น PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MOV เป็น PDF",
        link: "/l/mov-to-pdf",
      },
    ],
  },
  "mpeg-to-txt": {
    title: "แปลง MPEG เป็น TXT ออนไลน์ฟรี",
    displayTitle: "ตัวแปลง MPEG เป็น TXT",
    description:
      "แปลงไฟล์ MPEG ขนาด 1 ชั่วโมงเป็นไฟล์ TXT ในเวลาไม่ถึง 1 นาทีด้วย AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง MPEG เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MPEG",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ MPEG เป็นเอกสาร TXT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MPEG",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MPEG เป็นข้อความ",
        link: "/l/mpeg-to-text",
      },
      {
        text: "MPEG เป็น Word",
        link: "/l/mpeg-to-word",
      },
      {
        text: "MPEG เป็น PDF",
        link: "/l/mpeg-to-pdf",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM เป็น TXT",
        link: "/l/webm-to-txt",
      },
      {
        text: "OGG เป็น TXT",
        link: "/l/ogg-to-txt",
      },
    ],
  },
  "webm-to-txt": {
    title: "แปลง WebM เป็น TXT ออนไลน์ฟรี",
    displayTitle: "ตัวแปลง WebM เป็น TXT",
    description:
      "แปลงไฟล์ WebM ขนาด 1 ชั่วโมงเป็นไฟล์ TXT ในเวลาไม่ถึง 1 นาทีด้วยพลังของ AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง WebM เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ WebM",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ WebM เป็นเอกสาร TXT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ WebM",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "WebM เป็น PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG เป็น TXT",
        link: "/l/ogg-to-txt",
      },
      {
        text: "OPUS เป็น TXT",
        link: "/l/opus-to-txt",
      },
      {
        text: "MPEG เป็น TXT",
        link: "/l/mpeg-to-txt",
      },
    ],
  },
  "mov-to-text": {
    title: "แปลง MOV เป็นข้อความออนไลน์ฟรี",
    displayTitle: "ตัวแปลง MOV เป็นข้อความ",
    description:
      "แปลงไฟล์ MOV ขนาด 1 ชั่วโมงเป็นข้อความในเวลาไม่ถึง 1 นาทีด้วยพลังของ AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองใช้เลย!",
    howToUse: {
      title: "แปลง MOV เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MOV",
        step2: "คลิกถอดความ",
        step3: "ส่งออกผลลัพธ์ข้อความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ MOV เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MOV",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MOV เป็น PDF",
        link: "/l/mov-to-pdf",
      },
      {
        text: "MOV เป็น SRT",
        link: "/l/mov-to-srt",
      },
      {
        text: "MOV เป็นถอดความ",
        link: "/l/mov-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG เป็นข้อความ",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "mov-to-pdf": {
    title: "แปลง MOV เป็น PDF ออนไลน์ฟรี",
    displayTitle: "ตัวแปลง MOV เป็น PDF",
    description:
      "เปลี่ยนไฟล์ MOV ขนาด 1 ชั่วโมงเป็นเอกสาร PDF ในเวลาไม่ถึง 1 นาทีด้วยพลังของ AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง MOV เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MOV",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ MOV เป็นเอกสาร PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MOV",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MOV เป็นข้อความ",
        link: "/l/mov-to-text",
      },
      {
        text: "MOV เป็น SRT",
        link: "/l/mov-to-srt",
      },
      {
        text: "MOV เป็นการถอดความ",
        link: "/l/mov-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM เป็น PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MPEG เป็น PDF",
        link: "/l/mpeg-to-pdf",
      },
    ],
  },
  "mov-to-srt": {
    title: "แปลง MOV เป็น SRT ออนไลน์ฟรี",
    displayTitle: "ตัวแปลง MOV เป็น SRT",
    description:
      "แปลงไฟล์ MOV ความยาว 1 ชั่วโมงเป็นคำบรรยาย SRT ภายในเวลาไม่ถึง 1 นาทีด้วย AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง MOV เป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MOV",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ MOV เป็นคำบรรยาย SRT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MOV",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MOV เป็นข้อความ",
        link: "/l/mov-to-text",
      },
      {
        text: "MOV เป็น PDF",
        link: "/l/mov-to-pdf",
      },
      {
        text: "MOV เป็นถอดความ",
        link: "/l/mov-to-transcript",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "AAC เป็น SRT",
        link: "/l/aac-to-srt",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
    ],
  },
  "mov-to-transcript": {
    title: "แปลง MOV เป็น Transcript ออนไลน์ฟรี",
    displayTitle: "ตัวแปลง MOV เป็น Transcript",
    description:
      "เปลี่ยนไฟล์ MOV ขนาด 1 ชั่วโมงให้เป็น Transcript ในเวลาไม่ถึง 1 นาทีด้วย AI รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง MOV เป็น Transcript ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ MOV",
        step2: "คลิกถอดความ",
        step3: "ส่งออก Transcript",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ MOV เป็น Transcript ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ MOV",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MOV เป็นข้อความ",
        link: "/l/mov-to-text",
      },
      {
        text: "MOV เป็น PDF",
        link: "/l/mov-to-pdf",
      },
      {
        text: "MOV เป็น SRT",
        link: "/l/mov-to-srt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG เป็นข้อความ",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "aac-to-text": {
    title: "แปลง AAC เป็นข้อความในไม่กี่วินาทีฟรี",
    displayTitle: "เครื่องแปลง AAC เป็นข้อความที่ขับเคลื่อนด้วย AI",
    description:
      "แปลงไฟล์ AAC ขนาด 1 ชั่วโมงเป็นข้อความในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง AAC เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ AAC",
        step2: "คลิกถอดความ",
        step3: "ส่งออกข้อความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ AAC เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ AAC",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "AAC เป็น SRT",
        link: "/l/aac-to-srt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG เป็นข้อความ",
        link: "/l/ogg-to-text",
      },
      {
        text: "OPUS เป็นข้อความ",
        link: "/l/opus-to-text",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG เป็นข้อความ",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "aac-to-srt": {
    title: "แปลง AAC เป็น SRT ในไม่กี่วินาทีฟรี",
    displayTitle: "เครื่องแปลง AAC เป็น SRT ที่ขับเคลื่อนด้วย AI",
    description:
      "แปลงไฟล์ AAC ขนาด 1 ชั่วโมงเป็นคำบรรยาย SRT ในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง AAC เป็น SRT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ AAC",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น SRT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ AAC เป็นคำบรรยาย SRT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ AAC",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "AAC เป็นข้อความ",
        link: "/l/aac-to-text",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "MOV เป็น SRT",
        link: "/l/mov-to-srt",
      },
      {
        text: "OGG เป็นข้อความ",
        link: "/l/ogg-to-text",
      },
      {
        text: "OPUS เป็นข้อความ",
        link: "/l/opus-to-text",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
    ],
  },
  "ogg-to-text": {
    title: "แปลง OGG เป็นข้อความในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง OGG เป็นข้อความที่ขับเคลื่อนด้วย AI",
    description:
      "เปลี่ยนไฟล์ OGG ขนาด 1 ชั่วโมงเป็นข้อความในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง OGG เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ OGG",
        step2: "คลิกถอดความ",
        step3: "ส่งออกข้อความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ OGG เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ OGG",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "OGG เป็น TXT",
        link: "/l/ogg-to-txt",
      },
      {
        text: "OGG เป็น PDF",
        link: "/l/ogg-to-pdf",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "OPUS เป็นข้อความ",
        link: "/l/opus-to-text",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG เป็นข้อความ",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "ogg-to-txt": {
    title: "แปลง OGG เป็น TXT ในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง OGG เป็น TXT ที่ขับเคลื่อนด้วย AI",
    description:
      "แปลงไฟล์ OGG ขนาด 1 ชั่วโมงเป็นไฟล์ TXT ในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง OGG เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ OGG",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น TXT",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ OGG เป็นเอกสาร TXT ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ OGG",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "OGG เป็นข้อความ",
        link: "/l/ogg-to-text",
      },
      {
        text: "OGG เป็น PDF",
        link: "/l/ogg-to-pdf",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "OPUS เป็น TXT",
        link: "/l/opus-to-txt",
      },
      {
        text: "WebM เป็น TXT",
        link: "/l/webm-to-txt",
      },
      {
        text: "MPEG เป็น TXT",
        link: "/l/mpeg-to-txt",
      },
    ],
  },
  "ogg-to-pdf": {
    title: "แปลง OGG เป็น PDF ในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง OGG เป็น PDF ที่ขับเคลื่อนโดย AI",
    description:
      "แปลงไฟล์ OGG ขนาด 1 ชั่วโมงเป็นเอกสาร PDF ในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง OGG เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ OGG",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ OGG เป็นเอกสาร PDF ในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ OGG",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "OGG เป็นข้อความ",
        link: "/l/ogg-to-text",
      },
      {
        text: "OGG เป็น TXT",
        link: "/l/ogg-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "OPUS เป็น PDF",
        link: "/l/opus-to-pdf",
      },
      {
        text: "WebM เป็น PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MPEG เป็น PDF",
        link: "/l/mpeg-to-pdf",
      },
    ],
  },
  "opus-to-text": {
    title: "แปลง OPUS เป็นข้อความในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง OPUS เป็นข้อความที่ขับเคลื่อนด้วย AI",
    description:
      "แปลงไฟล์ OPUS ขนาด 1 ชั่วโมงเป็นข้อความในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง OPUS เป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ OPUS",
        step2: "คลิกถอดความ",
        step3: "ส่งออกข้อความ",
      },
    },
    features: [
      {
        title: "แปลงไฟล์ OPUS เป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลสำคัญจากไฟล์ OPUS",
      },
      {
        title: "ส่งออกข้อความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "OPUS เป็น PDF",
        link: "/l/opus-to-pdf",
      },
      {
        text: "OPUS เป็น TXT",
        link: "/l/opus-to-txt",
      },
      {
        text: "MP3 เป็นข้อความ",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG เป็นข้อความ",
        link: "/l/ogg-to-text",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG เป็นข้อความ",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "opus-to-pdf": {
    title: "แปลง OPUS เป็น PDF ในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง OPUS เป็น PDF ที่ขับเคลื่อนโดย AI",
    description:
      "แปลงไฟล์ OPUS ขนาด 1 ชั่วโมงเป็นเอกสาร PDF ในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดความเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง OPUS เป็น PDF ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ OPUS",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็น PDF",
      },
    },
  },
  "opus-to-txt": {
    title: "แปลง OPUS เป็น TXT ในไม่กี่วินาทีฟรี",
    displayTitle: "ตัวแปลง OPUS เป็น TXT ที่ขับเคลื่อนด้วย AI",
    description:
      "แปลงไฟล์ OPUS ขนาด 1 ชั่วโมงเป็นไฟล์ TXT ในเวลาไม่ถึง 1 นาที รองรับหลายรูปแบบและตัวเลือกการส่งออกที่ง่ายทำให้การถอดเสียงเป็นเรื่องง่าย ลองเลย!",
    howToUse: {
      title: "แปลง OPUS เป็น TXT ในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์ OPUS",
        step2: "คลิกถอดเสียง",
        step3: "ส่งออกเป็น TXT",
      },
    },
  },
  "speech-to-text": {
    title: "เครื่องแปลงเสียงเป็นข้อความออนไลน์ฟรี",
    displayTitle: "เครื่องแปลงเสียงเป็นข้อความออนไลน์ฟรี",
    description:
      "แปลงเสียงเป็นข้อความด้วยเทคโนโลยี AI ขั้นสูง การรู้จำเสียงที่รวดเร็ว แม่นยำ และเชื่อถือได้สำหรับความต้องการในการถอดความของคุณทั้งหมด",
    howToUse: {
      title: "แปลงเสียงเป็นข้อความในสามขั้นตอนง่ายๆ",
      stepTitles: {
        step1: "อัปโหลดไฟล์เสียง",
        step2: "คลิกถอดความ",
        step3: "ส่งออกเป็นข้อความ",
      },
    },
    features: [
      {
        title: "แปลงเสียงเป็นข้อความในไม่กี่วินาที",
      },
      {
        title: "สร้างสรุป แผนที่ความคิด และข้อมูลเชิงลึกจากการพูด",
      },
      {
        title: "ส่งออกการถอดความในรูปแบบต่างๆ (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 เป็นข้อความ",
        link: "/ล/mp3-เป็นข้อความ",
      },
      {
        text: "MP4 เป็นข้อความ",
        link: "/ล/mp4-เป็น-ข้อความ-ตัวแปลง",
      },
      {
        text: "M4A เป็นข้อความ",
        link: "/ล/m4a-เป็น-ข้อความ",
      },
      {
        text: "WAV เป็นข้อความ",
        link: "/ล/wav-to-text",
      },
      {
        text: "AAC เป็นข้อความ",
        link: "/a/aac-to-text",
      },
      {
        text: "OGG เป็นข้อความ",
        link: "/ล/ogg-to-text",
      },
      {
        text: "OPUS เป็นข้อความ",
        link: "/ล/โอปุส-ถึง-ข้อความ",
      },
      {
        text: "WebM เป็นข้อความ",
        link: "/ล/webm-to-text",
      },
    ],
  },
};
