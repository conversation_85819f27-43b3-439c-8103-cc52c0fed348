{"loading": "กำลังโหลด...", "meta": {"title": "เครื่องมือดึงข้อความจาก YouTube ฟรี | สร้างและดาวน์โหลดข้อความจาก YouTube", "description": "ดึงข้อความจากวิดีโอในคลิกเดียว ดาวน์โหลดคำบรรยายในหลายรูปแบบ (SRT, VTT, TXT, CSV) ไม่ต้องลงทะเบียน ใช้งานรวดเร็วและปลอดภัย", "ogTitle": "เครื่องมือสร้างถอดความ YouTube | ดึงและดาวน์โหลดถอดความวิดีโอฟรี", "ogDescription": "สร้างข้อความถอดเสียง YouTube ได้ทันทีฟรี Extract ข้อความวิดีโอด้วยการคลิกเพียงครั้งเดียว ดาวน์โหลดคำบรรยายในหลายรูปแบบ (SRT, VTT, TXT, CSV) ไม่ต้องลงทะเบียน ใช้งานรวดเร็วและปลอดภัย", "ogImageAlt": "UniScribe YouTube Transcript Extractor", "twitterTitle": "เครื่องมือสร้างถอดความ YouTube | ดึงและดาวน์โหลดถอดความวิดีโอฟรี", "twitterDescription": "ดึงข้อความถอดเสียงจากวิดีโอ YouTube ได้อย่างง่ายดาย ดาวน์โหลดคำบรรยายในหลายรูปแบบ (SRT, VTT, TXT, CSV) ไม่ต้องลงทะเบียน ใช้งานรวดเร็วและปลอดภัย", "schemaTitle": "เครื่องมือดึงข้อความจาก YouTube", "schemaDescription": "ดึงข้อความถอดเสียงจากวิดีโอ YouTube ได้อย่างง่ายดาย ดาวน์โหลดคำบรรยายในหลายรูปแบบ (SRT, VTT, TXT, CSV) ไม่ต้องลงทะเบียน ใช้งานรวดเร็วและปลอดภัย"}, "hero": {"title": "ดึงข้อความถอดเสียงจาก YouTube ฟรี", "description": "แปลงวิดีโอ YouTube เป็นข้อความได้อย่างง่ายดาย คัดลอกและดาวน์โหลดข้อความ YouTube ที่สร้างขึ้นในคลิกเดียว"}, "features": {"extraction": {"title": "การดึงข้อมูลถอดเสียงฟรี", "description": "ดึงข้อความถอดเสียงวิดีโอทั้งหมดพร้อมกับเวลา"}, "formats": {"title": "หลายรูปแบบการส่งออก", "description": "ดาวน์โหลดในรูปแบบ TXT, SRT, VTT และ CSV"}, "instant": {"title": "สร้างการถอดความทันที", "description": "รับการถอดเสียงทันทีโดยไม่ต้องลงทะเบียน"}}, "buttons": {"getTranscript": "รับข้อความถอดเสียง", "goToTranscribe": "ไปที่ถอดความ", "export": "ส่งออก"}, "preview": {"title": "ตัวอย่างวิดีโอ"}, "transcript": {"title": "ถอดความ", "includeTimestamps": "รวมเวลา"}, "faq": {"what": {"question": "YouTube Transcript Extractor คืออะไร?", "answer": "YouTube Transcript Extractor เป็นเครื่องมือฟรีที่ช่วยให้คุณสามารถดึงและดาวน์โหลดข้อความถอดเสียงจากวิดีโอ YouTube ได้"}, "howTo": {"question": "ฉันจะใช้ YouTube Transcript Extractor ได้อย่างไร?", "answer": "เพียงแค่วาง URL วิดีโอ YouTube และคลิกที่ 'Get Transcript'"}, "free": {"question": "YouTube Transcript Extractor ใช้งานได้ฟรีหรือไม่?", "answer": "ใช่ ฟีเจอร์การดึงข้อความถอดเสียงพื้นฐานนั้นฟรีโดยสิ้นเชิง"}, "formats": {"question": "ฉันสามารถดาวน์โหลดถอดความวิดีโอ YouTube ในรูปแบบต่างๆ ได้หรือไม่?", "answer": "ใช่, คุณสามารถดาวน์โหลดการถอดเสียงในรูปแบบ .txt, .srt, .vtt, และ .csv ได้."}, "private": {"question": "ฉันสามารถดึงข้อความถอดเสียงจากวิดีโอ YouTube ส่วนตัวได้หรือไม่?", "answer": "ไม่, การถอดความสามารถทำได้เฉพาะจากวิดีโอสาธารณะเท่านั้น."}, "limit": {"question": "มีขีดจำกัดในการดึงข้อมูลทรานสคริปต์หรือไม่?", "answer": "ไม่ ไม่มีขีดจำกัดจำนวนทรานสคริปต์ที่คุณสามารถดึงออกได้"}, "account": {"question": "ฉันต้องสร้างบัญชีเพื่อใช้ YouTube Transcript Extractor หรือไม่?", "answer": "การดูถอดความพื้นฐานฟรีโดยไม่ต้องมีบัญชี อย่างไรก็ตาม ฟีเจอร์เช่นการคัดลอกและดาวน์โหลดคำบรรยายต้องการการเข้าสู่ระบบบัญชีฟรี"}, "extract": {"question": "ฉันสามารถดึงข้อความถอดเสียงจากวิดีโอ YouTube ได้หรือไม่?", "answer": "หากวิดีโอมีการถอดความที่มีอยู่แล้ว เราสามารถดึงข้อมูลเหล่านั้นออกมาได้โดยตรง หากไม่มี คุณสามารถใช้ฟีเจอร์การถอดความ YouTube ของ Uniscribe เพื่อสร้างการถอดความได้"}}, "upgradeNotice": "ขออภัยในความไม่สะดวก! การถอดเสียง YouTube กำลังได้รับการปรับปรุงและจะกลับมาในไม่ช้า"}