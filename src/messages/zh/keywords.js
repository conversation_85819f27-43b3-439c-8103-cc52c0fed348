export const KEYWORD_PAGES = {
  "voice-to-text": {
    title: "语音转文本转换器 | 即时语音识别",
    displayTitle: "语音转文本转换器",
    description:
      "以专业级的准确性将语音录音转换为文本。非常适合会议、讲座和语音笔记。",
  },
  "mp3-to-text": {
    title: "在线 MP3 转文本转换器 - 免费 - 由 AI 驱动",
    displayTitle: "在线 MP3 转文本转换器",
    description:
      "使用 UniScribe，将 MP3 音频文件在几分钟内转换为文本。它支持 98 种语言，并可以将 MP3 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP3 转换为文字稿",
      stepTitles: {
        step1: "上传 MP3 文件",
        step2: "点击转录",
        step3: "导出文字稿",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MP3 音频文件转换为文本",
      },
      {
        title: "从 MP3 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文字稿 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 转 TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 转 Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 转 PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 转 SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "MP3 转 VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp3-to-vtt": {
    title: "免费在线将 MP3 转换为 VTT",
    displayTitle: "免费在线将 MP3 转换为 VTT",
    description:
      "UniScribe 是一项免费的在线转录服务，可以快速将 mp3 文件转换为准确的 vtt 字幕格式。它支持 98 种语言。它还会总结您的文件内容和要点，并生成思维导图，以帮助您提取重要信息。",
    howToUse: {
      title: "三步轻松将 MP3 转换为 VTT",
      stepTitles: {
        step1: "上传 MP3 文件",
        step2: "点击转录",
        step3: "导出为 VTT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MP3 音频文件转换为 VTT",
      },
      {
        title: "从 MP3 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 转 TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 转 Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 转 PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 转 SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp3-to-srt": {
    title: "免费在线将 MP3 转换为 SRT - 由 AI 驱动",
    displayTitle: "在线 MP3 到 SRT 转换器",
    description:
      "UniScribe 是一个免费的在线转录服务，快速将 mp3 文件转换为准确的 srt 字幕格式。它支持 98 种语言。它还总结了您文件的内容和要点，并生成思维导图，以帮助您提取重要信息。",
    howToUse: {
      title: "三步轻松将 MP3 转换为 SRT",
      stepTitles: {
        step1: "上传 MP3 文件",
        step2: "点击转录",
        step3: "导出为 SRT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MP3 音频文件转换为 SRT",
      },
      {
        title: "从 MP3 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 转 TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 转 Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 转 PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 转 VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp3-to-txt": {
    title: "免费在线将 MP3 转换为 TXT - 由 AI 驱动",
    displayTitle: "在线 MP3 到 TXT 转换器",
    description:
      "使用 UniScribe，免费在线将 MP3 音频文件在几分钟内转换为 txt 文件。它支持 98 种语言，并可以将 MP3 文件转换为 SRT、TXT、Word、PDF、CSV 等。此外，它基于 AI 技术自动生成摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP3 转换为 TXT",
      stepTitles: {
        step1: "上传 MP3 文件",
        step2: "点击转录",
        step3: "导出为 TXT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MP3 音频文件转换为 TXT",
      },
      {
        title: "从 MP3 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 到 SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "MP3 到 Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 到 PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 到 VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A 到文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 到文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp3-to-word": {
    title: "免费在线将 MP3 转换为 Word - 由 AI 提供支持",
    displayTitle: "在线 MP3 转 Word 转换器",
    description:
      "使用 UniScribe，在线免费将 MP3 音频文件在几分钟内转换为 Word 文档。它支持 98 种语言，并可以将 MP3 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP3 转换为 Word",
      stepTitles: {
        step1: "上传 MP3 文件",
        step2: "点击转录",
        step3: "导出为 Word",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MP3 音频文件转换为 Word 文档",
      },
      {
        title: "从 MP3 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本 (SRT、TXT、Word、PDF、CSV、VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 转 SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "MP3 转 TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 转 PDF",
        link: "/l/mp3-to-pdf",
      },
      {
        text: "MP3 转 VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp3-to-pdf": {
    title: "免费在线将 MP3 转换为 PDF - 由 AI 驱动",
    displayTitle: "在线 MP3 到 PDF 转换器",
    description:
      "使用 UniScribe，在线免费将 MP3 音频文件在几分钟内转换为 pdf 文件。它支持 98 种语言，可以将 MP3 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP3 转换为 PDF",
      stepTitles: {
        step1: "上传 MP3 文件",
        step2: "点击转录",
        step3: "导出为 PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MP3 音频文件转换为 PDF 文档",
      },
      {
        title: "从 MP3 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP3 转 SRT",
        link: "/l/mp3-to-srt",
      },
      {
        text: "MP3 转 TXT",
        link: "/l/mp3-to-txt",
      },
      {
        text: "MP3 转 Word",
        link: "/l/mp3-to-word",
      },
      {
        text: "MP3 转 VTT",
        link: "/l/mp3-to-vtt",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "ai-legal-transcription": {
    title: "人工智能法律转录 | 专业法庭报告",
    displayTitle: "人工智能法律转录",
    description:
      "专门的法律转录服务，采用人工智能技术。非常适合法庭程序、证词和法律文件。",
  },
  "best-transcription-software": {
    title: "最佳转录软件 | 基于AI的解决方案",
    displayTitle: "最佳转录软件",
    description: "体验最好的转录软件，采用先进的AI技术。快速、准确且易于使用。",
  },
  "audio-to-text-converter": {
    title: "免费的在线音频转文本转换器 | UniScribe - 快速且安全",
    displayTitle: "在线免费将音频转换为文本",
    description:
      "以专业的准确性将音频文件转换为文本。从您的本地设备上传音频和视频文件。点击“转录”将您的音频和视频在几秒钟内转换为文本。以各种格式导出转录文本或分享链接以直接查看",
  },
  "ai-audio-transcription": {
    title: "AI音频转录 | 智能转换解决方案",
    displayTitle: "AI音频转录",
    description: "先进的AI驱动音频转录服务。高准确率与智能语言处理技术。",
  },
  "ai-medical-transcription": {
    title: "人工智能医疗转录 | 医疗文档",
    displayTitle: "人工智能医疗转录",
    description:
      "专业的人工智能医疗转录服务。符合HIPAA标准，具备医学术语专业知识。",
  },
  "youtube-transcript-generator": {
    title: "YouTube 字幕生成器 | 内容创作工具",
    displayTitle: "YouTube 字幕生成器",
    description:
      "为 YouTube 视频生成准确的字幕。非常适合内容创作者和营销人员。",
  },
  "record-lectures-transcription": {
    title: "讲座录音转录应用 | 学习工具",
    displayTitle: "讲座录音转录",
    description: "自动录制和转录讲座。非常适合学生和学术用途。",
  },
  "google-speech-to-text": {
    title: "谷歌语音转文本替代方案 | 语音识别",
    displayTitle: "谷歌语音转文本替代方案",
    description: "专业的谷歌语音转文本替代方案。先进的功能和更好的准确性。",
  },
  "ai-meeting-summary": {
    title: "AI会议摘要生成器 | 智能会议记录",
    displayTitle: "AI会议摘要生成器",
    description: "使用AI自动生成会议摘要。将录音转换为可操作的会议记录。",
  },
  "lecture-transcription-app": {
    title: "讲座转录应用程序 | 学习助手",
    displayTitle: "讲座转录应用程序",
    description: "录制并转录讲座，智能组织。非常适合学生和教育工作者。",
  },
  "business-meeting-transcription": {
    title: "商务会议转录 | 专业服务",
    displayTitle: "商务会议转录",
    description: "专业的商务会议转录服务。准确的记录，快速的周转。",
  },
  "convert-audio-recording": {
    title: "将音频录音转换为文本 | 简易工具",
    displayTitle: "转换音频录音",
    description: "轻松将任何音频录音转换为文本。支持多种音频格式和语言。",
  },
  "classroom-recording-transcription": {
    title: "课堂录音转录 | 教育工具",
    displayTitle: "课堂录音转录",
    description: "自动转录课堂录音。非常适合学生、教师和远程学习。",
  },
  "podcast-to-text-converter": {
    title: "播客转文本转换器 | 内容工具",
    displayTitle: "播客转文本转换器",
    description: "轻松将播客剧集转换为文本。非常适合内容再利用和无障碍访问。",
  },
  "student-lecture-notes": {
    title: "学生讲座笔记 | AI学习助手",
    displayTitle: "学生讲座笔记",
    description: "为学生提供自动化讲座笔记。课堂上永远不会错过重要信息。",
  },
  "audio-file-converter": {
    title: "音频文件转换器 | 文本转录",
    displayTitle: "音频文件转换器",
    description: "将任何音频文件转换为文本格式。支持多种音频格式和语言。",
  },
  "broadcast-media-transcription": {
    title: "广播媒体转录 | 媒体工具",
    displayTitle: "广播媒体转录",
    description: "专业的广播媒体转录。非常适合电视、广播和数字内容。",
  },
  "academic-seminar-transcription": {
    title: "学术研讨会转录 | 研究工具",
    displayTitle: "学术研讨会转录",
    description: "学术研讨会的安全转录。用于研究和分析的准确文档。",
  },
  "youtube-to-text": {
    title: "YouTube 转文本转换器 | 视频转录工具",
    displayTitle: "YouTube 转文本转换器",
    description:
      "以专业的准确性将 YouTube 视频转换为文本。快速且安全。支持 98 种语言，并提供多种导出格式，包括 txt、docx、pdf、srt 等。它还总结了您视频的内容和要点，并生成思维导图以帮助您提取重要信息。",
    howToUse: {
      title: "通过三个简单步骤将 YouTube 转换为文本",
      stepTitles: {
        step1: "输入 YouTube URL",
        step2: "登录以转录",
        step3: "导出为转录本",
      },
    },
    features: [
      {
        title: "在几秒钟内将 YouTube 视频转换为文本",
      },
      {
        title: "从 YouTube 视频生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "YouTube 转 Word",
        link: "/l/youtube-to-word",
      },
      {
        text: "YouTube 转 TXT",
        link: "/l/youtube-to-txt",
      },
      {
        text: "YouTube 转 SRT",
        link: "/l/youtube-to-srt",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "音频转文本",
        link: "/l/audio-to-text",
      },
    ],
  },
  "youtube-to-word": {
    title: "YouTube到Word转换器 | 视频内容工具",
    displayTitle: "YouTube到Word转换器",
    description:
      "以专业的准确性将YouTube视频转换为Word文档。快速且安全。支持98种语言并提供多种导出格式。它还总结了您视频的内容和要点，并生成思维导图，以帮助您提取重要信息。",
    howToUse: {
      title: "在三个简单步骤中将YouTube转换为Word",
      stepTitles: {
        step1: "输入YouTube URL",
        step2: "点击转录",
        step3: "导出为Word",
      },
    },
    features: [
      {
        title: "在几秒钟内将YouTube视频转换为Word文档",
      },
      {
        title: "从YouTube视频生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "YouTube到文本",
        link: "/l/youtube-to-text",
      },
      {
        text: "YouTube到TXT",
        link: "/l/youtube-to-txt",
      },
      {
        text: "YouTube到SRT",
        link: "/l/youtube-to-srt",
      },
      {
        text: "MP4到Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "M4A到Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "WAV到Word",
        link: "/l/wav-to-word",
      },
      {
        text: "视频到Word",
        link: "/l/video-to-word",
      },
      {
        text: "音频到Word",
        link: "/l/audio-to-word",
      },
    ],
  },
  "youtube-to-txt": {
    title: "YouTube 到 TXT 转换器 | 简单文本导出",
    displayTitle: "YouTube 到 TXT 转换器",
    description:
      "以专业的准确性将 YouTube 视频转换为 TXT 文件。快速且安全。支持 98 种语言，并提供多种导出格式。它还总结了您视频的内容和要点，并生成思维导图，以帮助您提取重要信息。",
    howToUse: {
      title: "三步轻松将 YouTube 转换为 TXT",
      stepTitles: {
        step1: "输入 YouTube URL",
        step2: "点击转录",
        step3: "导出为 TXT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 YouTube 视频转换为 TXT 文件",
      },
      {
        title: "从 YouTube 视频生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "YouTube 到文本",
        link: "/l/youtube-to-text",
      },
      {
        text: "YouTube 到 Word",
        link: "/l/youtube-to-word",
      },
      {
        text: "YouTube 到 SRT",
        link: "/l/youtube-to-srt",
      },
      {
        text: "MP4 到 TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "M4A 到 TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "WAV 到 TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "视频到 TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "音频到 TXT",
        link: "/l/audio-to-txt",
      },
    ],
  },
  "youtube-to-srt": {
    title: "YouTube到SRT | 字幕文件生成器",
    displayTitle: "YouTube到SRT转换器",
    description:
      "以专业的准确性从YouTube视频创建SRT字幕文件。快速且安全。支持98种语言，并提供多种导出格式。它还总结了您视频的内容和要点，并生成思维导图，以帮助您提取重要信息。",
    howToUse: {
      title: "三步轻松将YouTube转换为SRT",
      stepTitles: {
        step1: "输入YouTube网址",
        step2: "点击转录",
        step3: "导出为SRT",
      },
    },
    features: [
      {
        title: "在几秒钟内将YouTube视频转换为SRT字幕",
      },
      {
        title: "从YouTube视频生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "YouTube到文本",
        link: "/l/youtube-to-text",
      },
      {
        text: "YouTube到Word",
        link: "/l/youtube-to-word",
      },
      {
        text: "YouTube到TXT",
        link: "/l/youtube-to-txt",
      },
      {
        text: "MP4到SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "M4A到SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "WAV到SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "视频到SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "音频到SRT",
        link: "/l/audio-to-srt",
      },
    ],
  },
  "mp4-to-text-converter": {
    title: "在线将 MP4 转换为文本 - 由 AI 提供支持",
    displayTitle: "在线 MP4 转文本转换器",
    description:
      "以高准确率将 MP4 视频转换为文本。快速且安全。支持 98 种语言，并提供多种导出格式，包括 txt、docx、pdf、srt 等。",
    howToUse: {
      title: "如何在三个简单步骤中将 MP4 转换为文本",
      stepTitles: {
        step1: "上传 MP4 文件",
        step2: "点击转录",
        step3: "导出为文本",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MP4 视频转录为文本",
      },
      {
        title: "从 MP4 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "MP4 转 SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 转 TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 转 PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 转 VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 转 Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 转转录文本",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "YouTube 转文本",
        link: "/l/youtube-to-text",
      },
      {
        text: "音频转文本",
        link: "/l/audio-to-text",
      },
    ],
  },
  "mp4-to-word": {
    title: "在线免费将 MP4 转换为 Word - 由 AI 提供支持",
    displayTitle: "在线 MP4 转 Word 转换器",
    description:
      "使用 UniScribe，在线免费将 MP4 视频转换为 Word 文档，仅需几分钟。它支持 98 种语言，并可以将 MP4 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP4 转换为 Word",
      stepTitles: {
        step1: "上传 MP4 文件",
        step2: "点击转录",
        step3: "导出为 Word",
      },
    },
    features: [
      {
        title: "快速准确地将 MP4 视频转换为 Word 文档",
      },
      {
        title: "从 MP4 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "导出为 Word 文档或其他格式（SRT、TXT、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 转 SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 转 TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 转 PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 转 VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 转转录",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-srt": {
    title: "在线将 MP4 转换为 SRT，免费 - 由 AI 提供支持",
    displayTitle: "在线 MP4 到 SRT 转换器",
    description:
      "使用 UniScribe，免费在线将 MP4 视频转录为 SRT 字幕，仅需几分钟。它支持 98 种语言，并可以将 MP4 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP4 转换为 SRT",
      stepTitles: {
        step1: "上传 MP4 文件",
        step2: "点击转录",
        step3: "导出为 SRT",
      },
    },
    features: [
      {
        title: "快速准确地将 MP4 视频转换为 SRT 字幕",
      },
      {
        title: "从 MP4 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "导出为 SRT 字幕或其他格式 (TXT、Word、PDF、CSV、VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 转 Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 转 TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 转 PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 转 VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 转录",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-pdf": {
    title: "免费在线将 MP4 转换为 PDF - 由 AI 提供支持",
    displayTitle: "在线 MP4 到 PDF 转换器",
    description:
      "使用 UniScribe，在线免费在几分钟内将 MP4 视频转换为 PDF 文档。它支持 98 种语言，并可以将 MP4 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成内容的摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP4 转换为 PDF",
      stepTitles: {
        step1: "上传 MP4 文件",
        step2: "点击转录",
        step3: "导出为 PDF",
      },
    },
    features: [
      {
        title: "快速准确地将 MP4 视频转换为 PDF 文档",
      },
      {
        title: "从 MP4 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "导出为 PDF 文档或其他格式 (SRT, TXT, Word, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 到 SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 到 TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 到 Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 到 VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 到转录",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A 到文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV 到文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-vtt": {
    title: "免费在线将 MP4 转换为 VTT - 由 AI 提供支持",
    displayTitle: "在线 MP4 到 VTT 转换器",
    description:
      "使用 UniScribe，在线免费将 MP4 视频转换为 VTT 字幕，仅需几分钟。它支持 98 种语言，并可以将 MP4 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP4 转换为 VTT",
      stepTitles: {
        step1: "上传 MP4 文件",
        step2: "点击转录",
        step3: "导出为 VTT",
      },
    },
    features: [
      {
        title: "快速准确地将 MP4 视频转录为 VTT 字幕",
      },
      {
        title: "从 MP4 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "导出为 VTT 字幕或其他格式（SRT、TXT、Word、PDF、CSV）",
      },
    ],
    converters: [
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 转 SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 转 TXT",
        link: "/l/mp4-to-txt",
      },
      {
        text: "MP4 转 Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 转 PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 转转录",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-txt": {
    title: "免费在线将 MP4 转换为 TXT - 由 AI 提供支持",
    displayTitle: "在线 MP4 到 TXT 转换器",
    description:
      "使用 UniScribe，免费在线将 MP4 视频转录为 TXT 文件，只需几分钟。它支持 98 种语言，并可以将 MP4 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "通过三个简单步骤将 MP4 转换为 TXT",
      stepTitles: {
        step1: "上传 MP4 文件",
        step2: "点击转录",
        step3: "导出为 TXT",
      },
    },
    features: [
      {
        title: "快速准确地将 MP4 视频转换为 TXT 文件",
      },
      {
        title: "从 MP4 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "导出为 TXT 文件或其他格式（SRT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "MP4 到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 到 SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 到 Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 到 PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 到 VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "MP4 到转录",
        link: "/l/mp4-to-transcript",
      },
      {
        text: "M4A 到文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV 到文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "mp4-to-transcript": {
    title: "免费在线将 MP4 转换为文字稿 - 由 AI 提供支持",
    displayTitle: "在线 MP4 到文字稿转换器",
    description:
      "使用 UniScribe，免费在线将 MP4 视频在几分钟内转换为文字稿。它支持 98 种语言，并可以将 MP4 文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 MP4 转换为文字稿",
      stepTitles: {
        step1: "上传 MP4 文件",
        step2: "点击转录",
        step3: "导出为文字稿",
      },
    },
    features: [
      {
        title: "快速准确地将 MP4 视频转换为文字稿",
      },
      {
        title: "从 MP4 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出为文字稿 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "MP4 转 SRT",
        link: "/l/mp4-to-srt",
      },
      {
        text: "MP4 转 Word",
        link: "/l/mp4-to-word",
      },
      {
        text: "MP4 转 PDF",
        link: "/l/mp4-to-pdf",
      },
      {
        text: "MP4 转 VTT",
        link: "/l/mp4-to-vtt",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "wav-to-text": {
    title: "在线免费将WAV转换为文本 | 转录WAV为文本",
    displayTitle: "在线WAV到文本转换器",
    description:
      "使用UniScribe的AI驱动在线转录工具快速准确地将WAV文件转换为文本。只需上传您的WAV文件，点击‘转录’，然后让UniScribe完成其余工作！",
    howToUse: {
      title: "三步轻松将WAV转换为文本",
      stepTitles: {
        step1: "上传WAV文件",
        step2: "点击转录",
        step3: "导出文本结果",
      },
    },
    features: [
      {
        title: "在几秒钟内将WAV和其他文件格式转换为文本",
      },
      {
        title: "从WAV文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录本（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "WAV到SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV到VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV到PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV到Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV到TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV到转录本",
        link: "/l/wav-to-transcript",
      },
      {
        text: "M4A到文本",
        link: "/l/m4a-to-text",
      },
    ],
    faqs: [
      {
        question: "我如何将WAV文件转换为文本？",
        answer:
          "1. 上传您的WAV文件并等待其完成。选择正确的语言可以获得更好的转录结果。2. 登录并开始转录 3. 以您喜欢的格式导出转录本。",
      },
      {
        question: "将WAV转换为文本需要多长时间？",
        answer:
          "根据我们的测试，一旦上传WAV文件，UniScribe通常可以在一分钟内转录一个1小时的WAV文件。这是市场上最快的转录工具之一。然而，最终的转录时间取决于文件的实际长度。",
      },
      {
        question: "将WAV转换为文本的准确性如何？",
        answer:
          "我们使用优化的Whisper AI模型准确转录多达98种语言的音频——即使有口音！然而，转录质量也取决于音频本身。为了获得最佳效果，请尽量减少背景噪音，并确保一次只有一个人说话。",
      },
      {
        question: "UniScribe支持哪些格式的转录本导出？",
        answer: "您可以以6种格式导出转录本：SRT, TXT, Word, PDF, CSV和VTT。",
      },
    ],
  },
  "wav-to-word": {
    title: "在线 WAV 转 Word 转换器，免费 | 通过 AI 转录 WAV 到 Word",
    displayTitle: "在线 WAV 转 Word 转换器",
    description:
      "使用 UniScribe 以高精度将 WAV 文件转换为 Word，支持 AI。几秒钟内将 WAV 文件转录为 Word。支持 98 种语言。它会自动生成摘要、思维导图和内容的关键见解",
    howToUse: {
      title: "三步轻松将 WAV 转换为 Word",
      stepTitles: {
        step1: "上传 WAV 文件",
        step2: "点击转录",
        step3: "导出为 Word",
      },
    },
    features: [
      {
        title: "在几秒钟内将 WAV 和其他文件格式转换为 Word 文档",
      },
      {
        title: "从 WAV 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出转录文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV 转 SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV 转 VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV 转 PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV 转 TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV 转转录文本",
        link: "/l/wav-to-transcript",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-srt": {
    title: "在线 WAV 转 SRT 转换器 | 将 WAV 转录为 SRT",
    displayTitle: "在线 WAV 转 SRT 转换器",
    description:
      "使用 UniScribe 以高精度将 WAV 文件转换为 SRT，支持 AI。几秒钟内将 WAV 文件转录为 SRT。支持 98 种语言。它会自动生成内容的摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 WAV 转换为 SRT",
      stepTitles: {
        step1: "上传 WAV 文件",
        step2: "点击转录",
        step3: "导出为 SRT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 WAV 和其他文件格式转换为 SRT",
      },
      {
        title: "从 WAV 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV 转 Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV 转 VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV 转 PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV 转 TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV 转转录文本",
        link: "/l/wav-to-transcript",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-vtt": {
    title: "在线 WAV 转 VTT 转换器 | 将 WAV 转录为 VTT",
    displayTitle: "在线 WAV 转 VTT 转换器",
    description: "从 WAV 音频生成 VTT 字幕。非常适合基于网络的音频内容。",
    howToUse: {
      title: "三步轻松将 WAV 转换为 VTT",
      stepTitles: {
        step1: "上传 WAV 文件",
        step2: "点击转录",
        step3: "导出为 VTT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 WAV 和其他文件格式转换为 VTT",
      },
      {
        title: "从 WAV 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV 转 SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV 转 Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV 转 PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV 转 TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV 转转录",
        link: "/l/wav-to-transcript",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-pdf": {
    title: "在线 WAV 转 PDF 转换器，免费 | 转录 WAV 到 PDF",
    displayTitle: "在线 WAV 转 PDF 转换器",
    description:
      "使用 UniScribe 将 WAV 文件高精度转换为 PDF，支持 AI。几秒钟内将 WAV 文件转录为 PDF。支持 98 种语言。它自动生成内容的摘要、思维导图和关键见解",
    howToUse: {
      title: "三步轻松将 WAV 转换为 PDF",
      stepTitles: {
        step1: "上传 WAV 文件",
        step2: "点击转录",
        step3: "导出为 PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将 WAV 和其他文件格式转换为 PDF",
      },
      {
        title: "从 WAV 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV 转 SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV 转 Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV 转 VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV 转 TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "WAV 转转录文本",
        link: "/l/wav-to-transcript",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-transcript": {
    title: "在线 WAV 转录转换器免费 | 转录 WAV 到转录",
    displayTitle: "在线 WAV 转录转换器",
    description:
      "使用 UniScribe 将 WAV 文件转换为高准确度的转录， powered by AI。几秒钟内将 WAV 文件转录为转录。支持 98 种语言。它会自动生成摘要、思维导图和内容的关键见解",
    howToUse: {
      title: "三步轻松将 WAV 转录",
      stepTitles: {
        step1: "上传 WAV 文件",
        step2: "点击转录",
        step3: "导出转录",
      },
    },
    features: [
      {
        title: "在几秒钟内将 WAV 和其他文件格式转换为转录",
      },
      {
        title: "从 WAV 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出转录 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV 转 SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV 转 Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV 转 VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV 转 PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV 转 TXT",
        link: "/l/wav-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "wav-to-txt": {
    title: "在线 WAV 转 TXT 转换器 | 将 WAV 转录为 TXT",
    displayTitle: "在线 WAV 转 TXT 转换器",
    description:
      "使用 UniScribe 以高精度将 WAV 文件转换为 TXT，支持 AI。几秒钟内将 WAV 文件转录为 TXT。支持 98 种语言。它会自动生成内容的摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 WAV 转换为 TXT",
      stepTitles: {
        step1: "上传 WAV 文件",
        step2: "点击转录",
        step3: "导出为 TXT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 WAV 和其他文件格式转换为 TXT",
      },
      {
        title: "从 WAV 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WAV 转 SRT",
        link: "/l/wav-to-srt",
      },
      {
        text: "WAV 转 Word",
        link: "/l/wav-to-word",
      },
      {
        text: "WAV 转 VTT",
        link: "/l/wav-to-vtt",
      },
      {
        text: "WAV 转 PDF",
        link: "/l/wav-to-pdf",
      },
      {
        text: "WAV 转转录文本",
        link: "/l/wav-to-transcript",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
    ],
  },
  "m4a-to-text": {
    title: "免费在线将 M4A 转换为文本 | 转录 M4A 为文本",
    displayTitle: "在线 M4A 转文本转换器",
    description:
      "如何在仅 1 分钟内将 1 小时的 M4A 文件转换为文本？使用 UniScribe，简单如 1-2-3：上传 -> 转录 -> 导出。它支持 98 种语言，并可以将 M4A 文件转换为 SRT、TXT、Word、PDF、CSV 和 VTT。此外，它还会自动生成内容的摘要、思维导图和关键见解。",
    howToUse: {
      title: "通过三个简单步骤将 M4A 转换为文本",
      stepTitles: {
        step1: "上传 M4A 文件",
        step2: "点击转录",
        step3: "导出文本结果",
      },
    },
    features: [
      {
        title: "在几秒钟内将 M4A 和其他文件格式转换为文本",
      },
      {
        title: "从 M4A 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "M4A 转 Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A 转 PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A 转 SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A 转 VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A 转转录",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "M4A 转 TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-txt": {
    title: "免费在线将 M4A 转换为 TXT | M4A 转文本",
    displayTitle: "在线 M4A 到 TXT 转换器",
    description:
      "如何在仅 1 分钟内将 1 小时的 M4A 文件转换为 txt？使用 UniScribe 将 M4A 音频文件高精度转换为 txt，支持 AI。几秒钟内将 M4A 文件转录为 txt。支持 98 种语言。它会自动生成内容的摘要、思维导图和关键见解。",
    howToUse: {
      title: "用三个简单步骤将 M4A 转换为 TXT",
      stepTitles: {
        step1: "上传 M4A 文件",
        step2: "点击转录",
        step3: "导出为 TXT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 M4A 和其他文件格式转换为 TXT",
      },
      {
        title: "从 M4A 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A 转 Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A 转 PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A 转 SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A 转 VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A 转转录",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-word": {
    title: "免费在线将 M4A 转换为 Word | M4A 转文本 - 由 AI 提供支持",
    displayTitle: "在线 M4A 转 Word 转换器",
    description:
      "如何在短短 1 分钟内将 1 小时的 M4A 文件转换为 Word？使用 UniScribe 以高准确度将 M4A 音频文件转换为 Word，支持 AI。几秒钟内将 M4A 文件转录为 Word。支持 98 种语言。它会自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "通过三个简单步骤将 M4A 转换为 Word",
      stepTitles: {
        step1: "上传 M4A 文件",
        step2: "点击转录",
        step3: "导出为 Word",
      },
    },
    features: [
      {
        title: "在几秒钟内将 M4A 和其他文件格式转换为 Word 文档",
      },
      {
        title: "从 M4A 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A 转 TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A 转 PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A 转 SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A 转 VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A 转抄本",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-pdf": {
    title: "在线将 M4A 转换为 PDF 免费 | M4A 转文本 - 由 AI 驱动",
    displayTitle: "在线 M4A 到 PDF 转换器",
    description:
      "如何在仅 1 分钟内将 1 小时的 M4A 文件转换为 PDF？使用 UniScribe 将 M4A 音频文件高精度转换为 PDF，支持 AI。几秒钟内将 M4A 文件转录为 PDF。支持 98 种语言。它自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 M4A 转换为 PDF",
      stepTitles: {
        step1: "上传 M4A 文件",
        step2: "点击转录",
        step3: "导出为 PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将 M4A 和其他文件格式转换为 PDF",
      },
      {
        title: "从 M4A 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A 转 TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A 转 Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A 转 SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A 转 VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A 转录",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-srt": {
    title: "在线将 M4A 转换为 SRT 免费 | M4A 转文本 - 由 AI 提供支持",
    displayTitle: "在线 M4A 到 SRT 转换器",
    description:
      "如何在仅 1 分钟内将 1 小时的 M4A 文件转换为 SRT？使用 UniScribe 将 M4A 音频文件高精度转换为 SRT，支持 AI。几秒钟内将 M4A 文件转录为 SRT。支持 98 种语言。它会自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "在三个简单步骤中将 M4A 转换为 SRT",
      stepTitles: {
        step1: "上传 M4A 文件",
        step2: "点击转录",
        step3: "导出为 SRT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 M4A 和其他文件格式转换为 SRT",
      },
      {
        title: "从 M4A 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A 转 TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A 转 Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A 转 PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A 转 VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "M4A 转录",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-vtt": {
    title: "在线将 M4A 转换为 VTT，免费 | M4A 转文本 - AI 驱动",
    displayTitle: "在线 M4A 到 VTT 转换器",
    description:
      "如何在仅 1 分钟内将 1 小时的 M4A 文件转换为 VTT？使用 UniScribe 以 AI 驱动的高精度将 M4A 音频文件转换为 VTT。几秒钟内将 M4A 文件转录为 VTT。支持 98 种语言。它会自动生成内容的摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将 M4A 转换为 VTT",
      stepTitles: {
        step1: "上传 M4A 文件",
        step2: "点击转录",
        step3: "导出为 VTT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 M4A 和其他文件格式转换为 VTT",
      },
      {
        title: "从 M4A 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A 转 TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A 转 Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A 转 PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A 转 SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A 转录",
        link: "/l/m4a-to-transcript",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "m4a-to-transcript": {
    title: "在线将 M4A 转换为文字稿，免费 | M4A 转文本 - 由 AI 提供支持",
    displayTitle: "在线 M4A 到文字稿转换器",
    description:
      "如何在仅 1 分钟内将 1 小时的 M4A 文件转换为文字稿？使用 UniScribe 将 M4A 音频文件高精度转换为文字稿，支持 AI。几秒钟内将 M4A 文件转录为文字稿。支持 98 种语言。它会自动生成摘要、思维导图和内容的关键见解。",
    howToUse: {
      title: "三步轻松将 M4A 转换为文字稿",
      stepTitles: {
        step1: "上传 M4A 文件",
        step2: "点击转录",
        step3: "导出文字稿",
      },
    },
    features: [
      {
        title: "在几秒钟内将 M4A 和其他文件格式转换为文字稿",
      },
      {
        title: "从 M4A 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文字稿 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "M4A 转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "M4A 转 TXT",
        link: "/l/m4a-to-txt",
      },
      {
        text: "M4A 转 Word",
        link: "/l/m4a-to-word",
      },
      {
        text: "M4A 转 PDF",
        link: "/l/m4a-to-pdf",
      },
      {
        text: "M4A 转 SRT",
        link: "/l/m4a-to-srt",
      },
      {
        text: "M4A 转 VTT",
        link: "/l/m4a-to-vtt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "transcribe-mp4": {
    title: "转录 MP4 | 精确的视频转录",
    displayTitle: "转录 MP4",
    description: "轻松将 MP4 视频转录为文本。非常适合内容创作者和教育工作者。",
  },
  "video-to-text": {
    title: "免费在几秒钟内将视频转换为文本",
    displayTitle: "由AI驱动的视频转文本转换器",
    description:
      "在不到1分钟的时间内将1小时的视频文件转换为文本文件。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将视频转换为文本",
      stepTitles: {
        step1: "上传视频文件",
        step2: "点击转录",
        step3: "导出转录文本",
      },
    },
    features: [
      {
        title: "通过AI在几秒钟内将视频文件转换为文本",
      },
      {
        title: "从视频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "视频转TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "视频转SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "视频转Word",
        link: "/l/video-to-word",
      },
      {
        text: "视频转PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "视频转VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "视频转转录文本",
        link: "/l/video-to-transcript",
      },
      {
        text: "M4A转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-txt": {
    title: "免费在几秒钟内将视频转换为TXT",
    displayTitle: "由AI驱动的视频到TXT转换器",
    description:
      "在不到1分钟的时间内将1小时的视频文件转换为txt文件。对多种格式的支持和简单的导出选项使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将视频转换为TXT",
      stepTitles: {
        step1: "上传视频文件",
        step2: "点击转录",
        step3: "导出为TXT",
      },
    },
    features: [
      {
        title: "在几秒钟内将视频文件转换为TXT文档",
      },
      {
        title: "从视频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "视频转SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "视频转Word",
        link: "/l/video-to-word",
      },
      {
        text: "视频转PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "视频转VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "视频转转录文本",
        link: "/l/video-to-transcript",
      },
      {
        text: "M4A转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-srt": {
    title: "免费在几秒钟内将视频转换为SRT",
    displayTitle: "由AI驱动的视频到SRT转换器",
    description:
      "在不到1分钟的时间内将1小时的视频文件转换为srt字幕文件。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将视频转换为SRT",
      stepTitles: {
        step1: "上传视频文件",
        step2: "点击转录",
        step3: "导出为SRT",
      },
    },
    features: [
      {
        title: "在几秒钟内将视频文件转换为SRT字幕",
      },
      {
        title: "从视频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出转录本（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "视频转TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "视频转Word",
        link: "/l/video-to-word",
      },
      {
        text: "视频转PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "视频转VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "视频转转录本",
        link: "/l/video-to-transcript",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-vtt": {
    title: "免费在几秒钟内将视频转换为VTT",
    displayTitle: "由AI驱动的视频到VTT转换器",
    description:
      "在不到1分钟的时间内将1小时的视频文件转换为vtt字幕文件。对多种格式的支持和简单的导出选项使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将视频转换为VTT",
      stepTitles: {
        step1: "上传视频文件",
        step2: "点击转录",
        step3: "导出为VTT",
      },
    },
    features: [
      {
        title: "在几秒钟内将视频文件转换为VTT字幕",
      },
      {
        title: "从视频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出转录本（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "视频转TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "视频转SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "视频转Word",
        link: "/l/video-to-word",
      },
      {
        text: "视频转PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "视频转转录本",
        link: "/l/video-to-transcript",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-pdf": {
    title: "免费在几秒钟内将视频转换为PDF",
    displayTitle: "由AI驱动的视频到PDF转换器",
    description:
      "使用UniScribe，在线免费在几分钟内将视频文件转换为PDF文档。它支持98种语言，并可以将视频文件转换为SRT、TXT、Word、PDF、CSV等格式。此外，它基于AI技术自动生成摘要、思维导图和关键见解。",
    howToUse: {
      title: "通过三个简单步骤将视频转换为PDF",
      stepTitles: {
        step1: "上传视频文件",
        step2: "点击转录",
        step3: "导出为PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将视频文件转换为PDF文档",
      },
      {
        title: "从视频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "视频转TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "视频转SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "视频转Word",
        link: "/l/video-to-word",
      },
      {
        text: "视频转VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "视频转转录文本",
        link: "/l/video-to-transcript",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-word": {
    title: "免费在几秒钟内将视频转换为Word",
    displayTitle: "由AI驱动的视频转Word转换器",
    description:
      "在不到1分钟的时间内将1小时的视频文件转换为Word文档。支持多种格式和简单的导出选项，使转录变得轻而易举。立即尝试！",
    howToUse: {
      title: "通过三个简单步骤将视频转换为Word",
      stepTitles: {
        step1: "上传视频文件",
        step2: "点击转录",
        step3: "导出为Word",
      },
    },
    features: [
      {
        title: "在几秒钟内将视频文件转换为Word文档",
      },
      {
        title: "从视频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "视频转TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "视频转SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "视频转PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "视频转VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "视频转转录文本",
        link: "/l/video-to-transcript",
      },
      {
        text: "M4A转文本",
        link: "/l/m4a-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "video-to-transcript": {
    title: "在线免费将视频转换为文字稿",
    displayTitle: "在线视频转文字稿转换器",
    description:
      "使用 UniScribe，在线免费将视频文件在几分钟内转换为准确的文字稿。它支持 98 种语言，并可以将视频文件转换为 SRT、TXT、Word、PDF、CSV 等格式。此外，它基于 AI 技术自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将视频转换为文字稿",
      stepTitles: {
        step1: "上传视频文件",
        step2: "点击转录",
        step3: "导出文字稿",
      },
    },
    features: [
      {
        title: "在几秒钟内将视频文件转换为准确的文字稿",
      },
      {
        title: "从视频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文字稿 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "视频转 TXT",
        link: "/l/video-to-txt",
      },
      {
        text: "视频转 SRT",
        link: "/l/video-to-srt",
      },
      {
        text: "视频转 Word",
        link: "/l/video-to-word",
      },
      {
        text: "视频转 PDF",
        link: "/l/video-to-pdf",
      },
      {
        text: "视频转 VTT",
        link: "/l/video-to-vtt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
    ],
  },
  "audio-to-text": {
    title: "免费在线将音频转换为文本 | 快速而准确",
    displayTitle: "由AI驱动的音频转文本转换器",
    description:
      "在不到1分钟的时间内将1小时的音频文件转换为文本。UniScribe支持多种音频格式，并为您的便利提供各种导出选项。",
    howToUse: {
      title: "三步轻松将音频转换为文本",
      stepTitles: {
        step1: "上传音频文件",
        step2: "点击转录",
        step3: "导出转录稿",
      },
    },
    features: [
      {
        title: "迅速将音频文件转换为文本",
      },
      {
        title: "从音频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录稿（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "音频转TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "音频转SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "音频转Word",
        link: "/l/audio-to-word",
      },
      {
        text: "音频转PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "音频转VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "YouTube转文本",
        link: "/l/youtube-to-text",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
    ],
  },
  "audio-to-txt": {
    title: "在线免费将音频转换为TXT - 快速准确",
    displayTitle: "由AI驱动的音频到TXT转换器",
    description:
      "使用UniScribe，将1小时的音频文件在1分钟内转换为txt文件。它支持98种语言，并可以将音频文件转换为SRT、TXT、Word、PDF、CSV等格式。",
    howToUse: {
      title: "三步轻松将音频转换为TXT",
      stepTitles: {
        step1: "上传音频文件",
        step2: "点击转录",
        step3: "导出为TXT",
      },
    },
    features: [
      {
        title: "快速将音频文件转换为TXT文件",
      },
      {
        title: "从音频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "导出为TXT或其他转录格式（SRT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "音频转文本",
        link: "/l/audio-to-text",
      },
      {
        text: "音频转SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "音频转Word",
        link: "/l/audio-to-word",
      },
      {
        text: "音频转PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "音频转VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "audio-to-word": {
    title: "在线免费将音频转换为Word",
    displayTitle: "由AI驱动的音频转Word转换器",
    description:
      "使用UniScribe，在不到1分钟的时间内将1小时的音频文件转录为Word文档。它支持98种语言，并可以将音频文件转换为SRT、TXT、Word、PDF、CSV等格式。",
    howToUse: {
      title: "通过三个简单步骤将音频转换为Word",
      stepTitles: {
        step1: "上传音频文件",
        step2: "点击转录",
        step3: "导出为Word",
      },
    },
    features: [
      {
        title: "在几秒钟内将音频文件转换为Word文档",
      },
      {
        title: "从音频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "音频转文本",
        link: "/l/audio-to-text",
      },
      {
        text: "音频转SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "音频转TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "音频转PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "音频转VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "audio-to-pdf": {
    title: "在线免费将音频转换为PDF",
    displayTitle: "由AI驱动的音频到PDF转换器",
    description:
      "使用UniScribe，在不到1分钟的时间内将1小时的音频文件转换为pdf文档。它支持98种语言，并可以将音频文件转换为SRT、TXT、Word、PDF、CSV等格式。",
    howToUse: {
      title: "通过三个简单步骤将音频转换为PDF",
      stepTitles: {
        step1: "上传音频文件",
        step2: "点击转录",
        step3: "导出为PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将音频文件转换为PDF文档",
      },
      {
        title: "从音频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出转录本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "音频转文本",
        link: "/l/audio-to-text",
      },
      {
        text: "音频转SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "音频转TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "音频转Word",
        link: "/l/audio-to-word",
      },
      {
        text: "音频转VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "视频转文本",
        link: "/l/video-to-text",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "WAV转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "audio-to-vtt": {
    title: "在线免费将音频转换为VTT",
    displayTitle: "由AI驱动的音频到VTT转换器",
    description:
      "使用UniScribe，在线免费将音频文件在几分钟内转换为VTT字幕格式。它支持98种语言，并可以将音频文件转换为SRT、TXT、Word、PDF、CSV等格式。此外，它基于AI技术自动生成内容摘要、思维导图和关键见解。",
    howToUse: {
      title: "三步轻松将音频转换为VTT",
      stepTitles: {
        step1: "上传音频文件",
        step2: "点击转录",
        step3: "导出为VTT",
      },
    },
    features: [
      {
        title: "在几秒钟内将音频文件转换为VTT字幕",
      },
      {
        title: "从音频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "音频转文本",
        link: "/l/audio-to-text",
      },
      {
        text: "音频转SRT",
        link: "/l/audio-to-srt",
      },
      {
        text: "音频转TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "音频转Word",
        link: "/l/audio-to-word",
      },
      {
        text: "音频转PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "audio-to-srt": {
    title: "免费在线将音频转换为SRT",
    displayTitle: "由AI驱动的音频到SRT转换器",
    description:
      "使用UniScribe，将1小时的音频文件在1分钟内转换为srt字幕文件。它支持98种语言，并可以将音频文件转换为SRT、TXT、Word、PDF、CSV等格式。",
    howToUse: {
      title: "三步轻松将音频转换为SRT",
      stepTitles: {
        step1: "上传音频文件",
        step2: "点击转录",
        step3: "导出为SRT",
      },
    },
    features: [
      {
        title: "在几秒钟内将音频文件转换为SRT字幕",
      },
      {
        title: "从音频文件生成摘要、思维导图和关键见解",
      },
      {
        title: "导出为SRT或其他转录格式（SRT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "音频转文本",
        link: "/l/audio-to-text",
      },
      {
        text: "音频转Word",
        link: "/l/audio-to-word",
      },
      {
        text: "音频转PDF",
        link: "/l/audio-to-pdf",
      },
      {
        text: "音频转TXT",
        link: "/l/audio-to-txt",
      },
      {
        text: "音频转VTT",
        link: "/l/audio-to-vtt",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV转文本",
        link: "/l/wav-to-text",
      },
    ],
  },
  "webm-to-text": {
    title: "免费在几秒钟内将WebM转换为文本",
    displayTitle: "WebM到文本转换器",
    description:
      "在不到1分钟的时间内将1小时的WebM文件转换为文本。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将WebM转换为文本",
      stepTitles: {
        step1: "上传WebM文件",
        step2: "点击转录",
        step3: "导出文本结果",
      },
    },
    features: [
      {
        title: "在几秒钟内将WebM文件转换为文本",
      },
      {
        title: "从WebM文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "WebM到TXT",
        link: "/l/webm-to-txt",
      },
      {
        text: "WebM到PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG到文本",
        link: "/l/ogg-to-text",
      },
      {
        text: "OPUS到文本",
        link: "/l/opus-to-text",
      },
      {
        text: "AAC到文本",
        link: "/l/aac-to-text",
      },
    ],
  },
  "webm-to-pdf": {
    title: "免费在几秒钟内将WebM转换为PDF",
    displayTitle: "WebM到PDF转换器",
    description:
      "在1分钟内将1小时的WebM文件转换为PDF文档，采用AI技术。支持多种格式和简单的导出选项，使转录变得轻而易举。立即尝试！",
    howToUse: {
      title: "通过三个简单步骤将WebM转换为PDF",
      stepTitles: {
        step1: "上传WebM文件",
        step2: "点击转录",
        step3: "导出为PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将WebM文件转换为PDF文档",
      },
      {
        title: "从WebM文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "WebM到文本",
        link: "/l/webm-to-text",
      },
      {
        text: "WebM到TXT",
        link: "/l/webm-to-txt",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG到PDF",
        link: "/l/ogg-to-pdf",
      },
      {
        text: "OPUS到PDF",
        link: "/l/opus-to-pdf",
      },
      {
        text: "MOV到PDF",
        link: "/l/mov-to-pdf",
      },
    ],
  },
  "mpeg-to-text": {
    title: "免费在几秒钟内将 MPEG 转换为文本",
    displayTitle: "MPEG 转文本转换器",
    description:
      "在 1 分钟内将 1 小时的 MPEG 文件转换为文本，采用 AI 技术。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将 MPEG 转换为文本",
      stepTitles: {
        step1: "上传 MPEG 文件",
        step2: "点击转录",
        step3: "导出文本",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MPEG 文件转换为文本",
      },
      {
        title: "从 MPEG 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MPEG 转 Word",
        link: "/l/mpeg-to-word",
      },
      {
        text: "MPEG 转 PDF",
        link: "/l/mpeg-to-pdf",
      },
      {
        text: "MPEG 转 TXT",
        link: "/l/mpeg-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM 转文本",
        link: "/l/webm-to-text",
      },
      {
        text: "MOV 转文本",
        link: "/l/mov-to-text",
      },
    ],
  },
  "mpeg-to-word": {
    title: "免费在几秒钟内将 MPEG 转换为 Word",
    displayTitle: "MPEG 转 Word 转换器",
    description:
      "在 1 分钟内将 1 小时的 MPEG 文件转换为 Word 文档，借助 AI 的强大支持。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将 MPEG 转换为 Word",
      stepTitles: {
        step1: "上传 MPEG 文件",
        step2: "点击转录",
        step3: "导出为 Word",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MPEG 文件转换为 Word 文档",
      },
      {
        title: "从 MPEG 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MPEG 转文本",
        link: "/l/mpeg-to-text",
      },
      {
        text: "MPEG 转 PDF",
        link: "/l/mpeg-to-pdf",
      },
      {
        text: "MPEG 转 TXT",
        link: "/l/mpeg-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM 转文本",
        link: "/l/webm-to-text",
      },
      {
        text: "MOV 转文本",
        link: "/l/mov-to-text",
      },
    ],
  },
  "mpeg-to-pdf": {
    title: "免费在线将 MPEG 转换为 PDF",
    displayTitle: "MPEG 到 PDF 转换器",
    description:
      "在 1 分钟内将 1 小时的 MPEG 文件转换为 PDF 文档，采用 AI 技术。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将 MPEG 转换为 PDF",
      stepTitles: {
        step1: "上传 MPEG 文件",
        step2: "点击转录",
        step3: "导出为 PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MPEG 文件转换为 PDF 文档",
      },
      {
        title: "从 MPEG 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "MPEG 转文本",
        link: "/l/mpeg-to-text",
      },
      {
        text: "MPEG 转 Word",
        link: "/l/mpeg-to-word",
      },
      {
        text: "MPEG 转 TXT",
        link: "/l/mpeg-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM 转 PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MOV 转 PDF",
        link: "/l/mov-to-pdf",
      },
    ],
  },
  "mpeg-to-txt": {
    title: "免费在线将 MPEG 转换为 TXT",
    displayTitle: "MPEG 到 TXT 转换器",
    description:
      "在 1 分钟内将 1 小时的 MPEG 文件转换为 TXT 文件，采用 AI 技术。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将 MPEG 转换为 TXT",
      stepTitles: {
        step1: "上传 MPEG 文件",
        step2: "点击转录",
        step3: "导出为 TXT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 MPEG 文件转换为 TXT 文档",
      },
      {
        title: "从 MPEG 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "MPEG 到文本",
        link: "/l/mpeg-to-text",
      },
      {
        text: "MPEG 到 Word",
        link: "/l/mpeg-to-word",
      },
      {
        text: "MPEG 到 PDF",
        link: "/l/mpeg-to-pdf",
      },
      {
        text: "MP3 到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM 到 TXT",
        link: "/l/webm-to-txt",
      },
      {
        text: "OGG 到 TXT",
        link: "/l/ogg-to-txt",
      },
    ],
  },
  "webm-to-txt": {
    title: "在线免费将 WebM 转换为 TXT",
    displayTitle: "WebM 到 TXT 转换器",
    description:
      "在 1 分钟内将 1 小时的 WebM 文件转换为 TXT 文件，采用 AI 技术。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将 WebM 转换为 TXT",
      stepTitles: {
        step1: "上传 WebM 文件",
        step2: "点击转录",
        step3: "导出为 TXT",
      },
    },
    features: [
      {
        title: "在几秒钟内将 WebM 文件转换为 TXT 文档",
      },
      {
        title: "从 WebM 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "WebM 到文本",
        link: "/l/webm-to-text",
      },
      {
        text: "WebM 到 PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MP3 到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG 到 TXT",
        link: "/l/ogg-to-txt",
      },
      {
        text: "OPUS 到 TXT",
        link: "/l/opus-to-txt",
      },
      {
        text: "MPEG 到 TXT",
        link: "/l/mpeg-to-txt",
      },
    ],
  },
  "mov-to-text": {
    title: "免费在线将MOV转换为文本",
    displayTitle: "MOV到文本转换器",
    description:
      "在1分钟内将1小时的MOV文件转换为文本，采用AI技术。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将MOV转换为文本",
      stepTitles: {
        step1: "上传MOV文件",
        step2: "点击转录",
        step3: "导出文本结果",
      },
    },
    features: [
      {
        title: "在几秒钟内将MOV文件转换为文本",
      },
      {
        title: "从MOV文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "MOV到PDF",
        link: "/l/mov-to-pdf",
      },
      {
        text: "MOV到SRT",
        link: "/l/mov-to-srt",
      },
      {
        text: "MOV到转录",
        link: "/l/mov-to-transcript",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM到文本",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG到文本",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "mov-to-pdf": {
    title: "免费在线将MOV转换为PDF",
    displayTitle: "MOV到PDF转换器",
    description:
      "在1分钟内将1小时的MOV文件转换为PDF文档，使用AI技术。支持多种格式和简单的导出选项，使转录变得轻而易举。立即尝试！",
    howToUse: {
      title: "三步轻松将MOV转换为PDF",
      stepTitles: {
        step1: "上传MOV文件",
        step2: "点击转录",
        step3: "导出为PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将MOV文件转换为PDF文档",
      },
      {
        title: "从MOV文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "MOV转文本",
        link: "/l/mov-to-text",
      },
      {
        text: "MOV转SRT",
        link: "/l/mov-to-srt",
      },
      {
        text: "MOV转转录",
        link: "/l/mov-to-transcript",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM转PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MPEG转PDF",
        link: "/l/mpeg-to-pdf",
      },
    ],
  },
  "mov-to-srt": {
    title: "在线免费将MOV转换为SRT",
    displayTitle: "MOV到SRT转换器",
    description:
      "在1分钟内将1小时的MOV文件转换为SRT字幕，使用AI技术。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将MOV转换为SRT",
      stepTitles: {
        step1: "上传MOV文件",
        step2: "点击转录",
        step3: "导出为SRT",
      },
    },
    features: [
      {
        title: "在几秒钟内将MOV文件转换为SRT字幕",
      },
      {
        title: "从MOV文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "MOV到文本",
        link: "/l/mov-to-text",
      },
      {
        text: "MOV到PDF",
        link: "/l/mov-to-pdf",
      },
      {
        text: "MOV到转录",
        link: "/l/mov-to-transcript",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "AAC到SRT",
        link: "/l/aac-to-srt",
      },
      {
        text: "WebM到文本",
        link: "/l/webm-to-text",
      },
    ],
  },
  "mov-to-transcript": {
    title: "免费在线转换MOV为转录本",
    displayTitle: "MOV到转录本转换器",
    description:
      "在1分钟内将1小时的MOV文件转换为转录本，采用AI技术。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将MOV转换为转录本",
      stepTitles: {
        step1: "上传MOV文件",
        step2: "点击转录",
        step3: "导出转录本",
      },
    },
    features: [
      {
        title: "在几秒钟内将MOV文件转换为转录本",
      },
      {
        title: "从MOV文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "MOV到文本",
        link: "/l/mov-to-text",
      },
      {
        text: "MOV到PDF",
        link: "/l/mov-to-pdf",
      },
      {
        text: "MOV到SRT",
        link: "/l/mov-to-srt",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "WebM到文本",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG到文本",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "aac-to-text": {
    title: "免费在几秒钟内将AAC转换为文本",
    displayTitle: "由AI驱动的AAC到文本转换器",
    description:
      "在不到1分钟的时间内将1小时的AAC文件转换为文本。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将AAC转换为文本",
      stepTitles: {
        step1: "上传AAC文件",
        step2: "点击转录",
        step3: "导出文本",
      },
    },
    features: [
      {
        title: "在几秒钟内将AAC文件转换为文本",
      },
      {
        title: "从AAC文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出文本（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "AAC到SRT",
        link: "/l/aac-to-srt",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG到文本",
        link: "/l/ogg-to-text",
      },
      {
        text: "OPUS到文本",
        link: "/l/opus-to-text",
      },
      {
        text: "WebM到文本",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG到文本",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "aac-to-srt": {
    title: "免费在几秒钟内将AAC转换为SRT",
    displayTitle: "由AI驱动的AAC到SRT转换器",
    description:
      "在不到1分钟的时间内将1小时的AAC文件转换为SRT字幕。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将AAC转换为SRT",
      stepTitles: {
        step1: "上传AAC文件",
        step2: "点击转录",
        step3: "导出为SRT",
      },
    },
    features: [
      {
        title: "在几秒钟内将AAC文件转换为SRT字幕",
      },
      {
        title: "从AAC文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "AAC转文本",
        link: "/l/aac-to-text",
      },
      {
        text: "MP3转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "MOV转SRT",
        link: "/l/mov-to-srt",
      },
      {
        text: "OGG转文本",
        link: "/l/ogg-to-text",
      },
      {
        text: "OPUS转文本",
        link: "/l/opus-to-text",
      },
      {
        text: "WebM转文本",
        link: "/l/webm-to-text",
      },
    ],
  },
  "ogg-to-text": {
    title: "免费在几秒钟内将OGG转换为文本",
    displayTitle: "由AI驱动的OGG到文本转换器",
    description:
      "在不到1分钟的时间内将1小时的OGG文件转换为文本。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将OGG转换为文本",
      stepTitles: {
        step1: "上传OGG文件",
        step2: "点击转录",
        step3: "导出文本",
      },
    },
    features: [
      {
        title: "在几秒钟内将OGG文件转换为文本",
      },
      {
        title: "从OGG文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT, TXT, Word, PDF, CSV, VTT）",
      },
    ],
    converters: [
      {
        text: "OGG到TXT",
        link: "/l/ogg-to-txt",
      },
      {
        text: "OGG到PDF",
        link: "/l/ogg-to-pdf",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "OPUS到文本",
        link: "/l/opus-to-text",
      },
      {
        text: "WebM到文本",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG到文本",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "ogg-to-txt": {
    title: "免费在几秒钟内将OGG转换为TXT",
    displayTitle: "由AI驱动的OGG到TXT转换器",
    description:
      "在不到1分钟的时间内将1小时的OGG文件转换为TXT文件。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将OGG转换为TXT",
      stepTitles: {
        step1: "上传OGG文件",
        step2: "点击转录",
        step3: "导出为TXT",
      },
    },
    features: [
      {
        title: "在几秒钟内将OGG文件转换为TXT文档",
      },
      {
        title: "从OGG文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以各种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "OGG到文本",
        link: "/l/ogg-to-text",
      },
      {
        text: "OGG到PDF",
        link: "/l/ogg-to-pdf",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "OPUS到TXT",
        link: "/l/opus-to-txt",
      },
      {
        text: "WebM到TXT",
        link: "/l/webm-to-txt",
      },
      {
        text: "MPEG到TXT",
        link: "/l/mpeg-to-txt",
      },
    ],
  },
  "ogg-to-pdf": {
    title: "免费在几秒钟内将OGG转换为PDF",
    displayTitle: "由AI驱动的OGG到PDF转换器",
    description:
      "在不到1分钟的时间内将1小时的OGG文件转换为PDF文档。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将OGG转换为PDF",
      stepTitles: {
        step1: "上传OGG文件",
        step2: "点击转录",
        step3: "导出为PDF",
      },
    },
    features: [
      {
        title: "在几秒钟内将OGG文件转换为PDF文档",
      },
      {
        title: "从OGG文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本（SRT、TXT、Word、PDF、CSV、VTT）",
      },
    ],
    converters: [
      {
        text: "OGG到文本",
        link: "/l/ogg-to-text",
      },
      {
        text: "OGG到TXT",
        link: "/l/ogg-to-txt",
      },
      {
        text: "MP3到文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4到文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV到文本",
        link: "/l/wav-to-text",
      },
      {
        text: "OPUS到PDF",
        link: "/l/opus-to-pdf",
      },
      {
        text: "WebM到PDF",
        link: "/l/webm-to-pdf",
      },
      {
        text: "MPEG到PDF",
        link: "/l/mpeg-to-pdf",
      },
    ],
  },
  "opus-to-text": {
    title: "免费在几秒钟内将 OPUS 转换为文本",
    displayTitle: "由 AI 驱动的 OPUS 转文本转换器",
    description:
      "在不到 1 分钟的时间内将 1 小时的 OPUS 文件转换为文本。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将 OPUS 转换为文本",
      stepTitles: {
        step1: "上传 OPUS 文件",
        step2: "点击转录",
        step3: "导出文本",
      },
    },
    features: [
      {
        title: "在几秒钟内将 OPUS 文件转换为文本",
      },
      {
        title: "从 OPUS 文件生成摘要、思维导图和关键见解",
      },
      {
        title: "以多种格式导出文本 (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      {
        text: "OPUS 转 PDF",
        link: "/l/opus-to-pdf",
      },
      {
        text: "OPUS 转 TXT",
        link: "/l/opus-to-txt",
      },
      {
        text: "MP3 转文本",
        link: "/l/mp3-to-text",
      },
      {
        text: "MP4 转文本",
        link: "/l/mp4-to-text-converter",
      },
      {
        text: "WAV 转文本",
        link: "/l/wav-to-text",
      },
      {
        text: "OGG 转文本",
        link: "/l/ogg-to-text",
      },
      {
        text: "WebM 转文本",
        link: "/l/webm-to-text",
      },
      {
        text: "MPEG 转文本",
        link: "/l/mpeg-to-text",
      },
    ],
  },
  "opus-to-pdf": {
    title: "免费在几秒钟内将 OPUS 转换为 PDF",
    displayTitle: "由 AI 驱动的 OPUS 到 PDF 转换器",
    description:
      "在不到 1 分钟的时间内将 1 小时的 OPUS 文件转换为 PDF 文档。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "通过三个简单步骤将 OPUS 转换为 PDF",
      stepTitles: {
        step1: "上传 OPUS 文件",
        step2: "点击转录",
        step3: "导出为 PDF",
      },
    },
  },
  "opus-to-txt": {
    title: "免费在几秒钟内将 OPUS 转换为 TXT",
    displayTitle: "由 AI 驱动的 OPUS 到 TXT 转换器",
    description:
      "在不到 1 分钟的时间内将 1 小时的 OPUS 文件转换为 TXT 文件。支持多种格式和简单的导出选项，使转录变得轻而易举。现在就试试吧！",
    howToUse: {
      title: "三步轻松将 OPUS 转换为 TXT",
      stepTitles: {
        step1: "上传 OPUS 文件",
        step2: "点击转录",
        step3: "导出为 TXT",
      },
    },
  },

  "speech-to-text": {
    title: "免费在线语音转文本转换器",
    displayTitle: "免费在线语音转文本转换器",
    description: "使用先进的人工智能技术将语音转换为文本。快速、准确且可靠的语音识别，满足您所有的转录需求。",
    howToUse: {
      title: "将语音转换为文本只需三个简单步骤",
      stepTitles: {
        step1: "上传音频文件",
        step2: "点击转录",
        step3: "导出为文本"
      }
    },
    features: [
      {
        title: "在几秒钟内将语音转换为文本"
      },
      {
        title: "从演讲中生成摘要、思维导图和关键见解"
      },
      {
        title: "以各种格式导出转录文本（SRT、TXT、Word、PDF、CSV、VTT）"
      }
    ],
    converters: [
      {
        text: "MP3 转文本",
        link: "/zh/mp3转文本"
      },
      {
        text: "MP4 转文本",
        link: "/zh/mp4-to-text-converter"
      },
      {
        text: "M4A 转文本",
        link: "/zh/m4a转文本"
      },
      {
        text: "WAV 转文本",
        link: "/zh/wav-to-text"
      },
      {
        text: "AAC 转文本",
        link: "/aac-to-text"
      },
      {
        text: "OGG 转换为文本",
        link: "/zh/ogg-to-text"
      },
      {
        text: "OPUS 转换为文本",
        link: "/zh/opus-to-text"
      },
      {
        text: "WebM 转文本",
        link: "/zh/webm-to-text"
      }
    ]
  }
};
