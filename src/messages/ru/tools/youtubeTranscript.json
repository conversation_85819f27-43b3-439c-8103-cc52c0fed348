{"loading": "Загрузка...", "meta": {"title": "Бесплатный извлекатель транскриптов YouTube | Генерируйте и загружайте транскрипты YouTube", "description": "Извлекайте транскрипты видео одним щелчком. Загружайте субтитры в нескольких форматах (SRT, VTT, TXT, CSV). Регистрация не требуется, быстро и безопасно.", "ogTitle": "Генератор транскрипции YouTube | Извлекайте и загружайте транскрипции видео бесплатно", "ogDescription": "Генерируйте транскрипты YouTube мгновенно и бесплатно. Извлекайте транскрипты видео всего одним кликом. Загружайте субтитры в нескольких форматах (SRT, VTT, TXT, CSV). Регистрация не требуется, быстро и безопасно.", "ogImageAlt": "UniScribe YouTube Transcript Extractor", "twitterTitle": "Генератор транскрипций YouTube | Извлекайте и загружайте транскрипции видео бесплатно", "twitterDescription": "Легко извлекайте транскрипты видео с YouTube. Загружайте субтитры в нескольких форматах (SRT, VTT, TXT, CSV). Регистрация не требуется, быстро и безопасно.", "schemaTitle": "Извлекатель транскриптов YouTube", "schemaDescription": "Легко извлекайте транскрипты видео с YouTube. Загружайте субтитры в нескольких форматах (SRT, VTT, TXT, CSV). Регистрация не требуется, быстро и безопасно."}, "hero": {"title": "Извлеките транскрипты YouTube бесплатно", "description": "Легко преобразуйте видео с YouTube в транскрипт, скопируйте и загрузите сгенерированный транскрипт YouTube одним щелчком."}, "features": {"extraction": {"title": "Бесплатное извлечение транскрипта", "description": "Извлеките полные видеотранскрипты с временными метками."}, "formats": {"title": "Несколько форматов экспорта", "description": "Скачать в форматах TXT, SRT, VTT и CSV"}, "instant": {"title": "Генерируйте транскрипты мгновенно", "description": "Получите транскрипты немедленно без регистрации"}}, "buttons": {"getTranscript": "Получить транскрипцию", "goToTranscribe": "Перейти к транскрипции", "export": "Экспорт"}, "preview": {"title": "Предварительный просмотр видео"}, "transcript": {"title": "Транскрипция", "includeTimestamps": "Включить временные метки"}, "faq": {"what": {"question": "Что такое YouTube Transcript Extractor?", "answer": "YouTube Transcript Extractor — это бесплатный инструмент, который позволяет извлекать и загружать транскрипты из видео на YouTube."}, "howTo": {"question": "Как использовать YouTube Transcript Extractor?", "answer": "Просто вставьте URL-адрес видео YouTube и нажмите 'Получить транскрипцию'."}, "free": {"question": "Является ли YouTube Transcript Extractor бесплатным для использования?", "answer": "Да, функция базового извлечения транскрипта полностью бесплатна."}, "formats": {"question": "Могу ли я скачать транскрипты видео с YouTube в различных форматах?", "answer": "Да, вы можете скачать транскрипты в форматах .txt, .srt, .vtt и .csv."}, "private": {"question": "Могу ли я извлекать транскрипты из частных видео на YouTube?", "answer": "Нет, транскрипты могут быть извлечены только из публичных видео."}, "limit": {"question": "Существует ли ограничение на количество транскриптов, которые я могу извлечь?", "answer": "Нет, нет ограничений на количество транскриптов, которые вы можете извлечь."}, "account": {"question": "Мне нужно создать учетную запись для использования YouTube Transcript Extractor?", "answer": "Базовый просмотр транскрипта бесплатен без учетной записи. Однако такие функции, как копирование и загрузка субтитров, требуют входа в бесплатную учетную запись."}, "extract": {"question": "Могу ли я извлечь транскрипты видео с YouTube?", "answer": "Если видео имеет существующие транскрипты, мы можем извлечь их напрямую. Если нет, вы можете использовать функцию транскрипции YouTube от Uniscribe для генерации транскрипта."}}, "upgradeNotice": "Извините за беспокойство! Транскрипция YouTube обновляется и скоро вернется."}