export const KEYWORD_PAGES = {
  "voice-to-text": {
    title: "Voice to Text Converter | Instant Speech Recognition",
    displayTitle: "Voice to Text Converter",
    description:
      "Transform voice recordings to text with professional-grade accuracy. Perfect for meetings, lectures, and voice notes.",
  },
  "speech-to-text": {
    title: "Free Online Speech to Text Converter",
    displayTitle: "Free Online Speech to Text Converter",
    description:
      "Convert speech to text with advanced AI technology. Fast, accurate, and reliable speech recognition for all your transcription needs.",
    howToUse: {
      title: "Convert Speech to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload Audio File",
        step2: "Click Transcribe",
        step3: "Export as Text",
      },
    },
    features: [
      { title: "Convert Speech to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from Speech",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "AAC to Text", link: "/l/aac-to-text" },
      { text: "OGG to Text", link: "/l/ogg-to-text" },
      { text: "OPUS to Text", link: "/l/opus-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
    ],
  },
  "mp3-to-text": {
    title: "MP3 Audio to Text Converter Online for Free - No Download",
    displayTitle: "Online MP3 to Text Converter Powered by AI",
    description:
      "Convert MP3 audio files into text in a few minutes.It supports 98 languages and can convert MP3 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "How to Convert MP3 to Text in Three Steps?",
      stepTitles: {
        step1: "Upload MP3 File",
        step2: "Click Transcribe",
        step3: "Export Text Result",
      },
    },
    features: [
      { title: "Convert MP3 Audio to Text Online in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP3 File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP3 to TXT", link: "/l/mp3-to-txt" },
      { text: "MP3 to Word", link: "/l/mp3-to-word" },
      { text: "MP3 to PDF", link: "/l/mp3-to-pdf" },
      { text: "MP3 to SRT", link: "/l/mp3-to-srt" },
      { text: "MP3 to VTT", link: "/l/mp3-to-vtt" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
    faqs: [
      {
        question: "How to convert MP3 audio to text online for free?",
        answer:
          "1) Upload your MP3 file 2) Click 'Sign in to Transcribe' button 3) Wait for the transcription to complete 4) Download your text in your preferred format (TXT, Word, PDF, etc.)",
      },
      {
        question: "What languages are supported for MP3 to text conversion?",
        answer:
          "UniScribe supports 98 languages for MP3 to text conversion, including English, Spanish, French, German, Chinese, Japanese, Korean, and many more. The AI system automatically detects the language in your audio file.",
      },
      {
        question: "How accurate is the MP3 to text conversion?",
        answer:
          "UniScribe uses advanced AI technology to achieve high accuracy in MP3 to text conversion. The accuracy typically ranges from 90-99% depending on the audio quality, speaker clarity, and background noise.",
      },
      {
        question: "What output formats are available for the transcription?",
        answer:
          "UniScribe offers multiple export formats including TXT, Word (DOCX), PDF, SRT (subtitles), VTT (web subtitles), and CSV. You can choose the format that best suits your needs.",
      },
      {
        question: "Is the MP3 to text converter free to use?",
        answer:
          "Yes, UniScribe offers a free tier that allows you to convert MP3 to text. Premium features are available for users who need additional capabilities like batch processing, priority processing, or advanced AI features.",
      },
      {
        question: "How long does it take to convert MP3 to text?",
        answer:
          "The conversion speed depends on the file size and length. Typically, a 1-hour MP3 file can be converted to text in less than 1 minutes with UniScribe. Processing time may vary based on server load and file quality.",
      },
      {
        question: "Is my MP3 file secure during the conversion process?",
        answer:
          "Yes, UniScribe takes security seriously. All file uploads are encrypted using SSL/TLS, and files are automatically deleted from our servers after processing. We never share your files with third parties.",
      },
    ],
  },
  "mp3-to-vtt": {
    title: "Convert MP3 to VTT for Free Online ",
    displayTitle: "Convert MP3 to VTT for Free Online",
    description:
      "UniScribe is a free online transcription service that quickly converts mp3 files to accurate vtt subtitle format. It supports 98 languages. It also summarizes the content and key points of your files and generates mind maps to help you extract important information.",
    howToUse: {
      title: "Convert MP3 to VTT in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP3 File",
        step2: "Click Transcribe",
        step3: "Export as VTT",
      },
    },
    features: [
      { title: "Convert MP3 Audio Files to VTT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP3 File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP3 to TXT", link: "/l/mp3-to-txt" },
      { text: "MP3 to Word", link: "/l/mp3-to-word" },
      { text: "MP3 to PDF", link: "/l/mp3-to-pdf" },
      { text: "MP3 to SRT", link: "/l/mp3-to-srt" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "mp3-to-srt": {
    title: "Convert MP3 to SRT for Free Online - Powered by AI",
    displayTitle: "Online MP3 to SRT Converter",
    description:
      "UniScribe is a free online transcription service that quickly converts mp3 files to accurate srt subtitle format. It supports 98 languages. It also summarizes the content and key points of your files and generates mind maps to help you extract important information.",
    howToUse: {
      title: "Convert MP3 to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP3 File",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert MP3 Audio Files to SRT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP3 File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP3 to TXT", link: "/l/mp3-to-txt" },
      { text: "MP3 to Word", link: "/l/mp3-to-word" },
      { text: "MP3 to PDF", link: "/l/mp3-to-pdf" },
      { text: "MP3 to VTT", link: "/l/mp3-to-vtt" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "mp3-to-txt": {
    title: "Convert MP3 to TXT for Free Online - Powered by AI",
    displayTitle: "Online MP3 to TXT Converter",
    description:
      "With UniScribe, turn MP3 audio files into txt files in a few minutes for free online. It supports 98 languages and can convert MP3 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert MP3 to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP3 File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert MP3 Audio Files to TXT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP3 File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP3 to SRT", link: "/l/mp3-to-srt" },
      { text: "MP3 to Word", link: "/l/mp3-to-word" },
      { text: "MP3 to PDF", link: "/l/mp3-to-pdf" },
      { text: "MP3 to VTT", link: "/l/mp3-to-vtt" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "mp3-to-word": {
    title: "Convert MP3 to Word for Free Online - Powered by AI",
    displayTitle: "Online MP3 to Word Converter",
    description:
      "Convert MP3 audio files into word documents in a few minutes for free online. It supports 98 languages and can convert MP3 files to SRT, TXT, DOCX, PDF, CSV, and more.",
    howToUse: {
      title: "Convert MP3 to DOCX in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP3 File",
        step2: "Click Transcribe",
        step3: "Export as DOCX",
      },
    },
    features: [
      { title: "Convert MP3 Audio Files to DOCX Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP3 File",
      },
      {
        title:
          "Export Text Result in DOCX or Other Formats (SRT, TXT, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP3 to SRT", link: "/l/mp3-to-srt" },
      { text: "MP3 to TXT", link: "/l/mp3-to-txt" },
      { text: "MP3 to PDF", link: "/l/mp3-to-pdf" },
      { text: "MP3 to VTT", link: "/l/mp3-to-vtt" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
    faqs: [
      {
        question: "How to convert MP3 audio to DOCX online for free?",
        answer:
          "1) Upload your MP3 file 2) Click 'Sign in to Transcribe' button 3) Wait for the transcription to complete 4) Download your text in DOCX format",
      },
      {
        question: "How accurate is the MP3 to DOCX conversion?",
        answer:
          "UniScribe uses advanced AI technology to achieve high accuracy in MP3 to DOCX conversion. The accuracy typically ranges from 90-99% depending on the audio quality, speaker clarity, and background noise.",
      },
    ],
  },
  "mp3-to-pdf": {
    title: "Convert MP3 to PDF for Free Online - Powered by AI",
    displayTitle: "Online MP3 to PDF Converter",
    description:
      "With UniScribe, turn MP3 audio files into pdf files in a few minutes for free online. It supports 98 languages and can convert MP3 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert MP3 to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP3 File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert MP3 Audio Files to PDF Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP3 File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP3 to SRT", link: "/l/mp3-to-srt" },
      { text: "MP3 to TXT", link: "/l/mp3-to-txt" },
      { text: "MP3 to Word", link: "/l/mp3-to-word" },
      { text: "MP3 to VTT", link: "/l/mp3-to-vtt" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "ai-legal-transcription": {
    title: "AI Legal Transcription | Professional Court Reporting",
    displayTitle: "AI Legal Transcription",
    description:
      "Specialized legal transcription service with AI technology. Perfect for court proceedings, depositions, and legal documentation.",
  },
  "best-transcription-software": {
    title: "Best Transcription Software | AI-Powered Solution",
    displayTitle: "Best Transcription Software",
    description:
      "Experience the best transcription software with advanced AI technology. Fast, accurate, and easy to use.",
  },
  "audio-to-text-converter": {
    title:
      "Online Audio to Text Converter for Free | UniScribe - Fast and Secure",
    displayTitle: "Convert Audio to Text for Free Online",
    description:
      "Convert audio files to text with professional accuracy.Upload audio and video files from your local device. Click 'Transcribe' to convert your audio and video into text in just few seconds.Export transcribed text in various formats or share a link to view it directly",
  },
  "ai-audio-transcription": {
    title: "AI Audio Transcription | Smart Converting Solution",
    displayTitle: "AI Audio Transcription",
    description:
      "Advanced AI-powered audio transcription service. High accuracy with smart language processing technology.",
  },
  "ai-medical-transcription": {
    title: "AI Medical Transcription | Healthcare Documentation",
    displayTitle: "AI Medical Transcription",
    description:
      "Specialized AI medical transcription service. HIPAA-compliant with medical terminology expertise.",
  },
  "youtube-transcript-generator": {
    title: "YouTube Transcript Generator | Content Creation Tool",
    displayTitle: "YouTube Transcript Generator",
    description:
      "Generate accurate transcripts for YouTube videos. Perfect for content creators and marketers.",
  },
  "record-lectures-transcription": {
    title: "Lecture Recording Transcription App | Study Tool",
    displayTitle: "Lecture Recording Transcription",
    description:
      "Record and transcribe lectures automatically. Perfect for students and academic purposes.",
  },
  "google-speech-to-text": {
    title: "Google Speech to Text Alternative | Voice Recognition",
    displayTitle: "Google Speech to Text Alternative",
    description:
      "Professional alternative to Google Speech-to-Text. Advanced features and better accuracy.",
  },
  "ai-meeting-summary": {
    title: "AI Meeting Summary Generator | Smart Minutes",
    displayTitle: "AI Meeting Summary Generator",
    description:
      "Generate meeting summaries automatically with AI. Transform recordings into actionable minutes.",
  },
  "lecture-transcription-app": {
    title: "Lecture Transcription App | Study Assistant",
    displayTitle: "Lecture Transcription App",
    description:
      "Record and transcribe lectures with smart organization. Perfect for students and educators.",
  },
  "business-meeting-transcription": {
    title: "Business Meeting Transcription | Professional Service",
    displayTitle: "Business Meeting Transcription",
    description:
      "Professional transcription service for business meetings. Accurate records with quick turnaround.",
  },
  "convert-audio-recording": {
    title: "Convert Audio Recording to Text | Easy Tool",
    displayTitle: "Convert Audio Recording",
    description:
      "Convert any audio recording to text easily. Support for multiple audio formats and languages.",
  },
  "classroom-recording-transcription": {
    title: "Classroom Recording Transcription | Education Tool",
    displayTitle: "Classroom Recording Transcription",
    description:
      "Transcribe classroom recordings automatically. Ideal for students, teachers, and distance learning.",
  },
  "podcast-to-text-converter": {
    title: "Podcast to Text Converter | Content Tool",
    displayTitle: "Podcast to Text Converter",
    description:
      "Convert podcast episodes to text easily. Perfect for content repurposing and accessibility.",
  },
  "student-lecture-notes": {
    title: "Student Lecture Notes | AI Study Assistant",
    displayTitle: "Student Lecture Notes",
    description:
      "Automated lecture notes for students. Never miss important information in class.",
  },
  "audio-file-converter": {
    title: "Audio File Converter | Text Transcription",
    displayTitle: "Audio File Converter",
    description:
      "Convert any audio file to text format. Support for multiple audio formats and languages.",
  },
  "broadcast-media-transcription": {
    title: "Broadcast Media Transcription | Media Tool",
    displayTitle: "Broadcast Media Transcription",
    description:
      "Professional transcription for broadcast media. Perfect for TV, radio, and digital content.",
  },
  "academic-seminar-transcription": {
    title: "Academic Seminar Transcription | Research Tool",
    displayTitle: "Academic Seminar Transcription",
    description:
      "Secure transcription for academic seminars. Accurate documentation for research and analysis.",
  },
  "youtube-to-text": {
    title: "YouTube to Text Converter | Video Transcription Tool",
    displayTitle: "YouTube to Text Converter",
    description:
      "Convert YouTube videos to text with professional accuracy. Fast and secure. Supports 98 languages and offers various export formats, including txt, docx, pdf, srt, and more. It also summarizes the content and key points of your videos and generates mind maps to help you extract important information.",
    howToUse: {
      title: "Convert YouTube to Text in Three Easy Steps",
      stepTitles: {
        step1: "Enter YouTube URL",
        step2: "Signin to Transcribe",
        step3: "Export as Transcript",
      },
    },
    features: [
      { title: "Convert YouTube Videos to Text in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from YouTube Videos",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "YouTube to Word", link: "/l/youtube-to-word" },
      { text: "YouTube to TXT", link: "/l/youtube-to-txt" },
      { text: "YouTube to SRT", link: "/l/youtube-to-srt" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "Audio to Text", link: "/l/audio-to-text" },
    ],
  },
  "youtube-to-word": {
    title: "YouTube to Word Converter | Video Content Tool",
    displayTitle: "YouTube to Word Converter",
    description:
      "Transform YouTube videos into Word documents with professional accuracy. Fast and secure. Supports 98 languages and offers various export formats. It also summarizes the content and key points of your videos and generates mind maps to help you extract important information.",
    howToUse: {
      title: "Convert YouTube to Word in Three Easy Steps",
      stepTitles: {
        step1: "Enter YouTube URL",
        step2: "Click Transcribe",
        step3: "Export as Word",
      },
    },
    features: [
      { title: "Convert YouTube Videos to Word Documents in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from YouTube Videos",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "YouTube to Text", link: "/l/youtube-to-text" },
      { text: "YouTube to TXT", link: "/l/youtube-to-txt" },
      { text: "YouTube to SRT", link: "/l/youtube-to-srt" },
      { text: "MP4 to Word", link: "/l/mp4-to-word" },
      { text: "M4A to Word", link: "/l/m4a-to-word" },
      { text: "WAV to Word", link: "/l/wav-to-word" },
      { text: "Video to Word", link: "/l/video-to-word" },
      { text: "Audio to Word", link: "/l/audio-to-word" },
    ],
  },
  "youtube-to-txt": {
    title: "YouTube to TXT Converter | Simple Text Export",
    displayTitle: "YouTube to TXT Converter",
    description:
      "Convert YouTube videos to TXT files with professional accuracy. Fast and secure. Supports 98 languages and offers various export formats. It also summarizes the content and key points of your videos and generates mind maps to help you extract important information.",
    howToUse: {
      title: "Convert YouTube to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Enter YouTube URL",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert YouTube Videos to TXT Files in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from YouTube Videos",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "YouTube to Text", link: "/l/youtube-to-text" },
      { text: "YouTube to Word", link: "/l/youtube-to-word" },
      { text: "YouTube to SRT", link: "/l/youtube-to-srt" },
      { text: "MP4 to TXT", link: "/l/mp4-to-txt" },
      { text: "M4A to TXT", link: "/l/m4a-to-txt" },
      { text: "WAV to TXT", link: "/l/wav-to-txt" },
      { text: "Video to TXT", link: "/l/video-to-txt" },
      { text: "Audio to TXT", link: "/l/audio-to-txt" },
    ],
  },
  "youtube-to-srt": {
    title: "YouTube to SRT | Subtitle File Generator",
    displayTitle: "YouTube to SRT Converter",
    description:
      "Create SRT subtitle files from YouTube videos with professional accuracy. Fast and secure. Supports 98 languages and offers various export formats. It also summarizes the content and key points of your videos and generates mind maps to help you extract important information.",
    howToUse: {
      title: "Convert YouTube to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Enter YouTube URL",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert YouTube Videos to SRT Subtitles in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from YouTube Videos",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "YouTube to Text", link: "/l/youtube-to-text" },
      { text: "YouTube to Word", link: "/l/youtube-to-word" },
      { text: "YouTube to TXT", link: "/l/youtube-to-txt" },
      { text: "MP4 to SRT", link: "/l/mp4-to-srt" },
      { text: "M4A to SRT", link: "/l/m4a-to-srt" },
      { text: "WAV to SRT", link: "/l/wav-to-srt" },
      { text: "Video to SRT", link: "/l/video-to-srt" },
      { text: "Audio to SRT", link: "/l/audio-to-srt" },
    ],
  },
  "mp4-to-text-converter": {
    title: "Convert MP4 to Text for Free Online - Powered by AI",
    displayTitle: "Online MP4 to Text Converter",
    description:
      "Convert MP4 videos to text with high accuracy. Fast and secure. Supports 98 languages and offers various export formats, including txt, docx, pdf, srt, and more.",
    howToUse: {
      title: "How to Convert MP4 to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP4 File",
        step2: "Click Transcribe",
        step3: "Export as Text",
      },
    },
    features: [
      { title: "Transcribe MP4 Videos to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP4 File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP4 to SRT", link: "/l/mp4-to-srt" },
      { text: "MP4 to TXT", link: "/l/mp4-to-txt" },
      { text: "MP4 to PDF", link: "/l/mp4-to-pdf" },
      { text: "MP4 to VTT", link: "/l/mp4-to-vtt" },
      { text: "MP4 to Word", link: "/l/mp4-to-word" },
      { text: "MP4 to Transcript", link: "/l/mp4-to-transcript" },
      { text: "YouTube to Text", link: "/l/youtube-to-text" },
      { text: "Audio to Text", link: "/l/audio-to-text" },
    ],
  },
  "mp4-to-word": {
    title: "Convert MP4 to Word for Free Online - Powered by AI",
    displayTitle: "Online MP4 to Word Converter",
    description:
      "With UniScribe, turn MP4 videos into Word documents in a few minutes for free online. It supports 98 languages and can convert MP4 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert MP4 to Word in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP4 File",
        step2: "Click Transcribe",
        step3: "Export as Word",
      },
    },
    features: [
      { title: "Convert MP4 Videos to Word Documents Fast and Accurate" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP4 File",
      },
      {
        title:
          "Export as Word Document or Other Formats (SRT, TXT, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "MP4 to SRT", link: "/l/mp4-to-srt" },
      { text: "MP4 to TXT", link: "/l/mp4-to-txt" },
      { text: "MP4 to PDF", link: "/l/mp4-to-pdf" },
      { text: "MP4 to VTT", link: "/l/mp4-to-vtt" },
      { text: "MP4 to Transcript", link: "/l/mp4-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "mp4-to-srt": {
    title: "Convert MP4 to SRT for Free Online - Powered by AI",
    displayTitle: "Online MP4 to SRT Converter",
    description:
      "With UniScribe, transcribe MP4 videos to SRT subtitles in a few minutes for free online. It supports 98 languages and can convert MP4 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert MP4 to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP4 File",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert MP4 Videos to SRT Subtitles Fast and Accurate" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP4 File",
      },
      {
        title:
          "Export as SRT Subtitle or Other Formats (TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "MP4 to Word", link: "/l/mp4-to-word" },
      { text: "MP4 to TXT", link: "/l/mp4-to-txt" },
      { text: "MP4 to PDF", link: "/l/mp4-to-pdf" },
      { text: "MP4 to VTT", link: "/l/mp4-to-vtt" },
      { text: "MP4 to Transcript", link: "/l/mp4-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "mp4-to-pdf": {
    title: "Convert MP4 to PDF for Free Online - Powered by AI",
    displayTitle: "Online MP4 to PDF Converter",
    description:
      "With UniScribe, convert MP4 videos to PDF documents in a few minutes for free online. It supports 98 languages and can convert MP4 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert MP4 to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP4 File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert MP4 Videos to PDF Documents Fast and Accurate" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP4 File",
      },
      {
        title:
          "Export as PDF Document or Other Formats (SRT, TXT, Word, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "MP4 to SRT", link: "/l/mp4-to-srt" },
      { text: "MP4 to TXT", link: "/l/mp4-to-txt" },
      { text: "MP4 to Word", link: "/l/mp4-to-word" },
      { text: "MP4 to VTT", link: "/l/mp4-to-vtt" },
      { text: "MP4 to Transcript", link: "/l/mp4-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "mp4-to-vtt": {
    title: "Convert MP4 to VTT for Free Online - Powered by AI",
    displayTitle: "Online MP4 to VTT Converter",
    description:
      "With UniScribe, convert MP4 videos to VTT subtitles in a few minutes for free online. It supports 98 languages and can convert MP4 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert MP4 to VTT in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP4 File",
        step2: "Click Transcribe",
        step3: "Export as VTT",
      },
    },
    features: [
      { title: "Transcribe MP4 Videos to VTT Subtitles Fast and Accurate" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP4 File",
      },
      {
        title:
          "Export as VTT Subtitle or Other Formats (SRT, TXT, Word, PDF, CSV)",
      },
    ],
    converters: [
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "MP4 to SRT", link: "/l/mp4-to-srt" },
      { text: "MP4 to TXT", link: "/l/mp4-to-txt" },
      { text: "MP4 to Word", link: "/l/mp4-to-word" },
      { text: "MP4 to PDF", link: "/l/mp4-to-pdf" },
      { text: "MP4 to Transcript", link: "/l/mp4-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "mp4-to-txt": {
    title: "Convert MP4 to TXT for Free Online - Powered by AI",
    displayTitle: "Online MP4 to TXT Converter",
    description:
      "With UniScribe, transcribe MP4 videos to TXT files in a few minutes for free online. It supports 98 languages and can convert MP4 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert MP4 to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP4 File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert MP4 Videos to TXT Files Fast and Accurate" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP4 File",
      },
      {
        title: "Export as TXT File or Other Formats (SRT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "MP4 to SRT", link: "/l/mp4-to-srt" },
      { text: "MP4 to Word", link: "/l/mp4-to-word" },
      { text: "MP4 to PDF", link: "/l/mp4-to-pdf" },
      { text: "MP4 to VTT", link: "/l/mp4-to-vtt" },
      { text: "MP4 to Transcript", link: "/l/mp4-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "mp4-to-transcript": {
    title: "Convert MP4 to Transcript for Free Online - Powered by AI",
    displayTitle: "Online MP4 to Transcript Converter",
    description:
      "With UniScribe, convert MP4 videos to transcript in a few minutes for free online. It supports 98 languages and can convert MP4 files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert MP4 to Transcript in Three Easy Steps",
      stepTitles: {
        step1: "Upload MP4 File",
        step2: "Click Transcribe",
        step3: "Export as Transcript",
      },
    },
    features: [
      { title: "Convert MP4 Videos to Transcript Fast and Accurate" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MP4 File",
      },
      {
        title:
          "Export as Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "MP4 to SRT", link: "/l/mp4-to-srt" },
      { text: "MP4 to Word", link: "/l/mp4-to-word" },
      { text: "MP4 to PDF", link: "/l/mp4-to-pdf" },
      { text: "MP4 to VTT", link: "/l/mp4-to-vtt" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },

  "wav-to-text": {
    title: "Convert WAV to Text Online for Free – AI-Powered Transcript",
    displayTitle: "Online WAV to Text Converter",
    description:
      "Use UniScribe to convert WAV files to text for free online. Transcribe WAV files to TXT in seconds. Supports 98 languages",
    howToUse: {
      title: "How to Convert WAV to Text in Three Steps?",
      stepTitles: {
        step1: "Upload WAV File",
        step2: "Click Transcribe",
        step3: "Export Text Result",
      },
    },
    features: [
      { title: "Convert WAV and Other File Formats to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WAV File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WAV to SRT", link: "/l/wav-to-srt" },
      { text: "WAV to VTT", link: "/l/wav-to-vtt" },
      { text: "WAV to PDF", link: "/l/wav-to-pdf" },
      { text: "WAV to Word", link: "/l/wav-to-word" },
      { text: "WAV to TXT", link: "/l/wav-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "WAV to Transcript", link: "/l/wav-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
    ],
    faqs: [
      {
        question: "How do I convert a WAV file to text?",
        answer:
          "1. Upload your WAV file and wait for it to finish. Selecting the correct language leads to better transcription results.2. Sign in and start transcribing 3. Export the transcript in your preferred format.",
      },
      {
        question: "How long does it take to convert WAV to Text?",
        answer:
          "Based on our tests, once a WAV file is uploaded, UniScribe can typically transcribe a 1-hour WAV file in under a minute. It's one of the fastest transcription tools out there. However, the final transcription time depends on the actual length of the file.",
      },
      {
        question: "How accurate is converting WAV to text?",
        answer:
          "We use an optimized Whisper AI model to transcribe audio accurately in up to 98 languages—even with accents! However, transcription quality also depends on the audio itself. For the best results, try to reduce background noise and ensure only one person speaks at a time.",
      },
      {
        question: "What formats does UniScribe support for transcript exports?",
        answer:
          "You can export the transcript in 6 formats: SRT, TXT, Word, PDF, CSV, and VTT.",
      },
    ],
  },
  "wav-to-word": {
    title:
      "Online WAV to Word Converter for Free | Transcribe WAV to Word by AI",
    displayTitle: "Online WAV to Word Converter",
    description:
      "Use UniScribe to convert WAV files to Word with high accuracy powered by AI. Transcribe WAV files to Word in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert WAV to Word in Three Easy Steps",
      stepTitles: {
        step1: "Upload WAV File",
        step2: "Click Transcribe",
        step3: "Export as Word",
      },
    },
    features: [
      {
        title: "Convert WAV and Other File Formats to Word Document in Seconds",
      },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WAV File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WAV to SRT", link: "/l/wav-to-srt" },
      { text: "WAV to VTT", link: "/l/wav-to-vtt" },
      { text: "WAV to PDF", link: "/l/wav-to-pdf" },
      { text: "WAV to TXT", link: "/l/wav-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "WAV to Transcript", link: "/l/wav-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
    ],
  },
  "wav-to-srt": {
    title: "Online WAV to SRT Converter for Free | Transcribe WAV to SRT",
    displayTitle: "Online WAV to SRT Converter",
    description:
      "Use UniScribe to convert WAV files to SRT with high accuracy powered by AI. Transcribe WAV files to SRT in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert WAV to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Upload WAV File",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert WAV and Other File Formats to SRT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WAV File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WAV to Word", link: "/l/wav-to-word" },
      { text: "WAV to VTT", link: "/l/wav-to-vtt" },
      { text: "WAV to PDF", link: "/l/wav-to-pdf" },
      { text: "WAV to TXT", link: "/l/wav-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "WAV to Transcript", link: "/l/wav-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
    ],
  },
  "wav-to-vtt": {
    title: "Online WAV to VTT Converter for Free | Transcribe WAV to VTT",
    displayTitle: "Online WAV to VTT Converter",
    description:
      "Generate VTT subtitles from WAV audio. Ideal for web-based audio content.",
    howToUse: {
      title: "Convert WAV to VTT in Three Easy Steps",
      stepTitles: {
        step1: "Upload WAV File",
        step2: "Click Transcribe",
        step3: "Export as VTT",
      },
    },
    features: [
      { title: "Convert WAV and Other File Formats to VTT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WAV File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WAV to SRT", link: "/l/wav-to-srt" },
      { text: "WAV to Word", link: "/l/wav-to-word" },
      { text: "WAV to PDF", link: "/l/wav-to-pdf" },
      { text: "WAV to TXT", link: "/l/wav-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "WAV to Transcript", link: "/l/wav-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
    ],
  },
  "wav-to-pdf": {
    title: "Online WAV to PDF Converter for Free | Transcribe WAV to PDF",
    displayTitle: "Online WAV to PDF Converter",
    description:
      "Use UniScribe to convert WAV files to PDF with high accuracy powered by AI. Transcribe WAV files to PDF in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert WAV to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload WAV File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert WAV and Other File Formats to PDF in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WAV File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WAV to SRT", link: "/l/wav-to-srt" },
      { text: "WAV to Word", link: "/l/wav-to-word" },
      { text: "WAV to VTT", link: "/l/wav-to-vtt" },
      { text: "WAV to TXT", link: "/l/wav-to-txt" },
      { text: "WAV to Transcript", link: "/l/wav-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
    ],
  },
  "wav-to-transcript": {
    title:
      "Online WAV to Transcript Converter for Free | Transcribe WAV to Transcript",
    displayTitle: "Online WAV to Transcript Converter",
    description:
      "Use UniScribe to convert WAV files to transcript with high accuracy powered by AI. Transcribe WAV files to transcript in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert WAV to Transcript in Three Easy Steps",
      stepTitles: {
        step1: "Upload WAV File",
        step2: "Click Transcribe",
        step3: "Export Transcript",
      },
    },
    features: [
      { title: "Convert WAV and Other File Formats to Transcript in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WAV File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WAV to SRT", link: "/l/wav-to-srt" },
      { text: "WAV to Word", link: "/l/wav-to-word" },
      { text: "WAV to VTT", link: "/l/wav-to-vtt" },
      { text: "WAV to PDF", link: "/l/wav-to-pdf" },
      { text: "WAV to TXT", link: "/l/wav-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
    ],
  },
  "wav-to-txt": {
    title: "Online WAV to TXT Converter for Free | Transcribe WAV to TXT",
    displayTitle: "Online WAV to TXT Converter",
    description:
      "Use UniScribe to convert WAV files to TXT with high accuracy powered by AI. Transcribe WAV files to TXT in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert WAV to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload WAV File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert WAV and Other File Formats to TXT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WAV File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WAV to SRT", link: "/l/wav-to-srt" },
      { text: "WAV to Word", link: "/l/wav-to-word" },
      { text: "WAV to VTT", link: "/l/wav-to-vtt" },
      { text: "WAV to PDF", link: "/l/wav-to-pdf" },
      { text: "WAV to Transcript", link: "/l/wav-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
    ],
  },
  "m4a-to-text": {
    title: "Convert M4A to Text Online for Free | Transcribe M4A to Text",
    displayTitle: "Online M4A to Text Converter",
    description:
      "How to turn a 1-hour M4A file into text in just 1 minute? With UniScribe, it's as easy as 1-2-3: upload -> transcribe -> export. It supports 98 languages and can convert M4A files to SRT, TXT, Word, PDF, CSV, and VTT. Plus, it automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert M4A to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload M4A File",
        step2: "Click Transcribe",
        step3: "Export Text Result",
      },
    },
    features: [
      { title: "Convert M4A and Other File Formats to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from M4A File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "M4A to Word", link: "/l/m4a-to-word" },
      { text: "M4A to PDF", link: "/l/m4a-to-pdf" },
      { text: "M4A to SRT", link: "/l/m4a-to-srt" },
      { text: "M4A to VTT", link: "/l/m4a-to-vtt" },
      { text: "M4A to Transcript", link: "/l/m4a-to-transcript" },
      { text: "M4A to TXT", link: "/l/m4a-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "m4a-to-txt": {
    title: "Convert M4A to TXT Online for Free | M4A to Text",
    displayTitle: "Online M4A to TXT Converter",
    description:
      "How to turn a 1-hour M4A file into txt in just 1 minute? Use UniScribe to convert M4A audio files to txt with high accuracy powered by AI. Transcribe M4A files to txt in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert M4A to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload M4A File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert M4A and Other File Formats to TXT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from M4A File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "M4A to Word", link: "/l/m4a-to-word" },
      { text: "M4A to PDF", link: "/l/m4a-to-pdf" },
      { text: "M4A to SRT", link: "/l/m4a-to-srt" },
      { text: "M4A to VTT", link: "/l/m4a-to-vtt" },
      { text: "M4A to Transcript", link: "/l/m4a-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "m4a-to-word": {
    title: "Convert M4A to Word Online for Free | M4A to Text - Powered by AI",
    displayTitle: "Online M4A to Word Converter",
    description:
      "How to turn a 1-hour M4A file into Word in just 1 minute? Use UniScribe to convert M4A audio files to Word with high accuracy powered by AI. Transcribe M4A files to Word in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert M4A to Word in Three Easy Steps",
      stepTitles: {
        step1: "Upload M4A File",
        step2: "Click Transcribe",
        step3: "Export as Word",
      },
    },
    features: [
      {
        title: "Convert M4A and Other File Formats to Word Document in Seconds",
      },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from M4A File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "M4A to TXT", link: "/l/m4a-to-txt" },
      { text: "M4A to PDF", link: "/l/m4a-to-pdf" },
      { text: "M4A to SRT", link: "/l/m4a-to-srt" },
      { text: "M4A to VTT", link: "/l/m4a-to-vtt" },
      { text: "M4A to Transcript", link: "/l/m4a-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "m4a-to-pdf": {
    title: "Convert M4A to PDF Online for Free | M4A to Text - Powered by AI",
    displayTitle: "Online M4A to PDF Converter",
    description:
      "How to turn a 1-hour M4A file into PDF in just 1 minute? Use UniScribe to convert M4A audio files to PDF with high accuracy powered by AI. Transcribe M4A files to PDF in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert M4A to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload M4A File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert M4A and Other File Formats to PDF in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from M4A File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "M4A to TXT", link: "/l/m4a-to-txt" },
      { text: "M4A to Word", link: "/l/m4a-to-word" },
      { text: "M4A to SRT", link: "/l/m4a-to-srt" },
      { text: "M4A to VTT", link: "/l/m4a-to-vtt" },
      { text: "M4A to Transcript", link: "/l/m4a-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "m4a-to-srt": {
    title: "Convert M4A to SRT Online for Free | M4A to Text - Powered by AI",
    displayTitle: "Online M4A to SRT Converter",
    description:
      "How to turn a 1-hour M4A file into SRT in just 1 minute? Use UniScribe to convert M4A audio files to SRT with high accuracy powered by AI. Transcribe M4A files to SRT in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert M4A to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Upload M4A File",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert M4A and Other File Formats to SRT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from M4A File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "M4A to TXT", link: "/l/m4a-to-txt" },
      { text: "M4A to Word", link: "/l/m4a-to-word" },
      { text: "M4A to PDF", link: "/l/m4a-to-pdf" },
      { text: "M4A to VTT", link: "/l/m4a-to-vtt" },
      { text: "M4A to Transcript", link: "/l/m4a-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "m4a-to-vtt": {
    title: "Convert M4A to VTT Online for Free | M4A to Text - Powered by AI",
    displayTitle: "Online M4A to VTT Converter",
    description:
      "How to turn a 1-hour M4A file into VTT in just 1 minute? Use UniScribe to convert M4A audio files to VTT with high accuracy powered by AI. Transcribe M4A files to VTT in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert M4A to VTT in Three Easy Steps",
      stepTitles: {
        step1: "Upload M4A File",
        step2: "Click Transcribe",
        step3: "Export as VTT",
      },
    },
    features: [
      { title: "Convert M4A and Other File Formats to VTT in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from M4A File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "M4A to TXT", link: "/l/m4a-to-txt" },
      { text: "M4A to Word", link: "/l/m4a-to-word" },
      { text: "M4A to PDF", link: "/l/m4a-to-pdf" },
      { text: "M4A to SRT", link: "/l/m4a-to-srt" },
      { text: "M4A to Transcript", link: "/l/m4a-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "m4a-to-transcript": {
    title:
      "Convert M4A to Transcript Online for Free | M4A to Text - Powered by AI",
    displayTitle: "Online M4A to Transcript Converter",
    description:
      "How to turn a 1-hour M4A file into transcript in just 1 minute? Use UniScribe to convert M4A audio files to transcript with high accuracy powered by AI. Transcribe M4A files to transcript in seconds. Supports 98 languages. It automatically generates summaries, mind maps, and key insights from the content",
    howToUse: {
      title: "Convert M4A to Transcript in Three Easy Steps",
      stepTitles: {
        step1: "Upload M4A File",
        step2: "Click Transcribe",
        step3: "Export Transcript",
      },
    },
    features: [
      { title: "Convert M4A and Other File Formats to Transcript in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from M4A File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "M4A to TXT", link: "/l/m4a-to-txt" },
      { text: "M4A to Word", link: "/l/m4a-to-word" },
      { text: "M4A to PDF", link: "/l/m4a-to-pdf" },
      { text: "M4A to SRT", link: "/l/m4a-to-srt" },
      { text: "M4A to VTT", link: "/l/m4a-to-vtt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "transcribe-mp4": {
    title: "Transcribe MP4 | Accurate Video Transcription",
    displayTitle: "Transcribe MP4",
    description:
      "Easily transcribe MP4 videos to text. Perfect for content creators and educators.",
  },
  "video-to-text": {
    title: "Convert Video to Text in Seconds for Free",
    displayTitle: "Video to Text Converter Powered by AI",
    description:
      "Turn a 1-hour video file into a text file in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert Video to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload Video File",
        step2: "Click Transcribe",
        step3: "Export Transcript",
      },
    },
    features: [
      { title: "Convert Video Files to Text in Seconds by AI" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Video File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Video to TXT", link: "/l/video-to-txt" },
      { text: "Video to SRT", link: "/l/video-to-srt" },
      { text: "Video to Word", link: "/l/video-to-word" },
      { text: "Video to PDF", link: "/l/video-to-pdf" },
      { text: "Video to VTT", link: "/l/video-to-vtt" },
      { text: "Video to Transcript", link: "/l/video-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "video-to-txt": {
    title: "Convert Video to TXT in Seconds for Free",
    displayTitle: "Video to TXT Converter Powered by AI",
    description:
      "Turn a 1-hour video file into a txt file in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert Video to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload Video File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert Video Files to TXT Documents in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Video File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "Video to SRT", link: "/l/video-to-srt" },
      { text: "Video to Word", link: "/l/video-to-word" },
      { text: "Video to PDF", link: "/l/video-to-pdf" },
      { text: "Video to VTT", link: "/l/video-to-vtt" },
      { text: "Video to Transcript", link: "/l/video-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "video-to-srt": {
    title: "Convert Video to SRT in Seconds for Free",
    displayTitle: "Video to SRT Converter Powered by AI",
    description:
      "Turn a 1-hour video file into a srt subtitle file in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert Video to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Upload Video File",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert Video Files to SRT Subtitles in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Video File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "Video to TXT", link: "/l/video-to-txt" },
      { text: "Video to Word", link: "/l/video-to-word" },
      { text: "Video to PDF", link: "/l/video-to-pdf" },
      { text: "Video to VTT", link: "/l/video-to-vtt" },
      { text: "Video to Transcript", link: "/l/video-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "video-to-vtt": {
    title: "Convert Video to VTT in Seconds for Free",
    displayTitle: "Video to VTT Converter Powered by AI",
    description:
      "Turn a 1-hour video file into a vtt subtitle file in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert Video to VTT in Three Easy Steps",
      stepTitles: {
        step1: "Upload Video File",
        step2: "Click Transcribe",
        step3: "Export as VTT",
      },
    },
    features: [
      { title: "Convert Video Files to VTT Subtitles in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Video File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "Video to TXT", link: "/l/video-to-txt" },
      { text: "Video to SRT", link: "/l/video-to-srt" },
      { text: "Video to Word", link: "/l/video-to-word" },
      { text: "Video to PDF", link: "/l/video-to-pdf" },
      { text: "Video to Transcript", link: "/l/video-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "video-to-pdf": {
    title: "Convert Video to PDF in Seconds for Free",
    displayTitle: "Video to PDF Converter Powered by AI",
    description:
      "With UniScribe, turn video files into PDF documents in a few minutes for free online. It supports 98 languages and can convert video files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert Video to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload Video File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert Video Files to PDF Documents in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Video File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "Video to TXT", link: "/l/video-to-txt" },
      { text: "Video to SRT", link: "/l/video-to-srt" },
      { text: "Video to Word", link: "/l/video-to-word" },
      { text: "Video to VTT", link: "/l/video-to-vtt" },
      { text: "Video to Transcript", link: "/l/video-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "video-to-word": {
    title: "Convert Video to Word in Seconds for Free",
    displayTitle: "Video to Word Converter Powered by AI",
    description:
      "Turn a 1-hour video file into a word document in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert Video to Word in Three Easy Steps",
      stepTitles: {
        step1: "Upload Video File",
        step2: "Click Transcribe",
        step3: "Export as Word",
      },
    },
    features: [
      { title: "Convert Video Files to Word Documents in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Video File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "Video to TXT", link: "/l/video-to-txt" },
      { text: "Video to SRT", link: "/l/video-to-srt" },
      { text: "Video to PDF", link: "/l/video-to-pdf" },
      { text: "Video to VTT", link: "/l/video-to-vtt" },
      { text: "Video to Transcript", link: "/l/video-to-transcript" },
      { text: "M4A to Text", link: "/l/m4a-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "video-to-transcript": {
    title: "Convert Video to Transcript for Free Online",
    displayTitle: "Online Video to Transcript Converter",
    description:
      "With UniScribe, turn video files into accurate transcripts in a few minutes for free online. It supports 98 languages and can convert video files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert Video to Transcript in Three Easy Steps",
      stepTitles: {
        step1: "Upload Video File",
        step2: "Click Transcribe",
        step3: "Export Transcript",
      },
    },
    features: [
      { title: "Convert Video Files to Accurate Transcripts in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Video File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "Video to TXT", link: "/l/video-to-txt" },
      { text: "Video to SRT", link: "/l/video-to-srt" },
      { text: "Video to Word", link: "/l/video-to-word" },
      { text: "Video to PDF", link: "/l/video-to-pdf" },
      { text: "Video to VTT", link: "/l/video-to-vtt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
    ],
  },
  "audio-to-text": {
    title: "Convert Audio to Text for Free Online | Fast and Accurate",
    displayTitle: "Audio to Text Converter Powered by AI",
    description:
      "Convert a 1-hour audio file to text in under 1 minute.UniScribe supports multiple audio formats and offers various export options for your convenience.",
    howToUse: {
      title: "Convert Audio to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload Audio File",
        step2: "Click Transcribe",
        step3: "Export Transcript",
      },
    },
    features: [
      { title: "Convert Audio Files to Text in No Time" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Audio File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Audio to TXT", link: "/l/audio-to-txt" },
      { text: "Audio to SRT", link: "/l/audio-to-srt" },
      { text: "Audio to Word", link: "/l/audio-to-word" },
      { text: "Audio to PDF", link: "/l/audio-to-pdf" },
      { text: "Audio to VTT", link: "/l/audio-to-vtt" },
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "YouTube to Text", link: "/l/youtube-to-text" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
    ],
  },
  "audio-to-txt": {
    title: "Convert Audio to TXT for Free Online - Fast and Accurate",
    displayTitle: "Audio to TXT Converter Powered by AI",
    description:
      "With UniScribe, turn a 1-hour audio file into a txt file in under 1 minute. It supports 98 languages and can convert audio files to SRT, TXT, Word, PDF, CSV, and more.",
    howToUse: {
      title: "Convert Audio to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload Audio File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert Audio Files to TXT Files in No Time" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Audio File",
      },
      {
        title:
          "Export as TXT or Other Transcript Formats (SRT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Audio to Text", link: "/l/audio-to-text" },
      { text: "Audio to SRT", link: "/l/audio-to-srt" },
      { text: "Audio to Word", link: "/l/audio-to-word" },
      { text: "Audio to PDF", link: "/l/audio-to-pdf" },
      { text: "Audio to VTT", link: "/l/audio-to-vtt" },
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "audio-to-word": {
    title: "Convert Audio to Word for Free Online ",
    displayTitle: "Audio to Word Converter Powered by AI",
    description:
      "With UniScribe, transcribe a 1-hour audio file into a word document in under 1 minute. It supports 98 languages and can convert audio files to SRT, TXT, Word, PDF, CSV, and more.",
    howToUse: {
      title: "Convert Audio to Word in Three Easy Steps",
      stepTitles: {
        step1: "Upload Audio File",
        step2: "Click Transcribe",
        step3: "Export as Word",
      },
    },
    features: [
      { title: "Convert Audio Files to Word Documents in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Audio File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Audio to Text", link: "/l/audio-to-text" },
      { text: "Audio to SRT", link: "/l/audio-to-srt" },
      { text: "Audio to TXT", link: "/l/audio-to-txt" },
      { text: "Audio to PDF", link: "/l/audio-to-pdf" },
      { text: "Audio to VTT", link: "/l/audio-to-vtt" },
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "audio-to-pdf": {
    title: "Convert Audio to PDF for Free Online",
    displayTitle: "Audio to PDF Converter Powered by AI",
    description:
      "With UniScribe, turn a 1-hour audio file into a pdf document in under 1 minute. It supports 98 languages and can convert audio files to SRT, TXT, Word, PDF, CSV, and more.",
    howToUse: {
      title: "Convert Audio to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload Audio File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert Audio Files to PDF Documents in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Audio File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Audio to Text", link: "/l/audio-to-text" },
      { text: "Audio to SRT", link: "/l/audio-to-srt" },
      { text: "Audio to TXT", link: "/l/audio-to-txt" },
      { text: "Audio to Word", link: "/l/audio-to-word" },
      { text: "Audio to VTT", link: "/l/audio-to-vtt" },
      { text: "Video to Text", link: "/l/video-to-text" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "audio-to-vtt": {
    title: "Convert Audio to VTT for Free Online",
    displayTitle: "Audio to VTT Converter Powered by AI",
    description:
      "With UniScribe, turn audio files into VTT subtitle format in a few minutes for free online. It supports 98 languages and can convert audio files to SRT, TXT, Word, PDF, CSV, and more. Plus, it automatically generates summaries, mind maps, and key insights from the content based on AI technology",
    howToUse: {
      title: "Convert Audio to VTT in Three Easy Steps",
      stepTitles: {
        step1: "Upload Audio File",
        step2: "Click Transcribe",
        step3: "Export as VTT",
      },
    },
    features: [
      { title: "Convert Audio Files to VTT Subtitles in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Audio File",
      },
      {
        title:
          "Export Transcript in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Audio to Text", link: "/l/audio-to-text" },
      { text: "Audio to SRT", link: "/l/audio-to-srt" },
      { text: "Audio to TXT", link: "/l/audio-to-txt" },
      { text: "Audio to Word", link: "/l/audio-to-word" },
      { text: "Audio to PDF", link: "/l/audio-to-pdf" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "audio-to-srt": {
    title: "Convert Audio to SRT for Free Online",
    displayTitle: "Audio to SRT Converter Powered by AI",
    description:
      "With UniScribe, turn a 1-hour audio file into a srt subtitle file in under 1 minute. It supports 98 languages and can convert audio files to SRT, TXT, Word, PDF, CSV, and more.",
    howToUse: {
      title: "Convert Audio to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Upload Audio File",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert Audio Files to SRT Subtitles in Seconds" },
      {
        title:
          "Generate Summaries, Mind Maps, and Key Insights from Audio File",
      },
      {
        title:
          "Export as SRT or Other Transcript Formats (SRT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "Audio to Text", link: "/l/audio-to-text" },
      { text: "Audio to Word", link: "/l/audio-to-word" },
      { text: "Audio to PDF", link: "/l/audio-to-pdf" },
      { text: "Audio to TXT", link: "/l/audio-to-txt" },
      { text: "Audio to VTT", link: "/l/audio-to-vtt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
    ],
  },
  "webm-to-text": {
    title: "Convert WebM to Text in Seconds for Free",
    displayTitle: "WebM to Text Converter",
    description:
      "Turn a 1-hour WebM file into text in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert WebM to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload WebM File",
        step2: "Click Transcribe",
        step3: "Export Text Result",
      },
    },
    features: [
      { title: "Convert WebM Files to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WebM File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WebM to TXT", link: "/l/webm-to-txt" },
      { text: "WebM to PDF", link: "/l/webm-to-pdf" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "OGG to Text", link: "/l/ogg-to-text" },
      { text: "OPUS to Text", link: "/l/opus-to-text" },
      { text: "AAC to Text", link: "/l/aac-to-text" },
    ],
  },
  "webm-to-pdf": {
    title: "Convert WebM to PDF in Seconds for Free",
    displayTitle: "WebM to PDF Converter",
    description:
      "Turn a 1-hour WebM file into a PDF document in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert WebM to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload WebM File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert WebM Files to PDF Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WebM File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "WebM to TXT", link: "/l/webm-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "OGG to PDF", link: "/l/ogg-to-pdf" },
      { text: "OPUS to PDF", link: "/l/opus-to-pdf" },
      { text: "MOV to PDF", link: "/l/mov-to-pdf" },
    ],
  },
  "mpeg-to-text": {
    title: "Convert MPEG to Text in Seconds for Free",
    displayTitle: "MPEG to Text Converter",
    description:
      "Turn a 1-hour MPEG file into text in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert MPEG to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload MPEG File",
        step2: "Click Transcribe",
        step3: "Export Text",
      },
    },
    features: [
      { title: "Convert MPEG Files to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MPEG File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MPEG to Word", link: "/l/mpeg-to-word" },
      { text: "MPEG to PDF", link: "/l/mpeg-to-pdf" },
      { text: "MPEG to TXT", link: "/l/mpeg-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "MOV to Text", link: "/l/mov-to-text" },
    ],
  },
  "mpeg-to-word": {
    title: "Convert MPEG to Word in Seconds for Free",
    displayTitle: "MPEG to Word Converter",
    description:
      "Turn a 1-hour MPEG file into a Word document in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert MPEG to Word in Three Easy Steps",
      stepTitles: {
        step1: "Upload MPEG File",
        step2: "Click Transcribe",
        step3: "Export as Word",
      },
    },
    features: [
      { title: "Convert MPEG Files to Word Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MPEG File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MPEG to Text", link: "/l/mpeg-to-text" },
      { text: "MPEG to PDF", link: "/l/mpeg-to-pdf" },
      { text: "MPEG to TXT", link: "/l/mpeg-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "MOV to Text", link: "/l/mov-to-text" },
    ],
  },
  "mpeg-to-pdf": {
    title: "Convert MPEG to PDF Online for Free",
    displayTitle: "MPEG to PDF Converter",
    description:
      "Turn a 1-hour MPEG file into a PDF document in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert MPEG to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload MPEG File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert MPEG Files to PDF Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MPEG File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MPEG to Text", link: "/l/mpeg-to-text" },
      { text: "MPEG to Word", link: "/l/mpeg-to-word" },
      { text: "MPEG to TXT", link: "/l/mpeg-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WebM to PDF", link: "/l/webm-to-pdf" },
      { text: "MOV to PDF", link: "/l/mov-to-pdf" },
    ],
  },
  "mpeg-to-txt": {
    title: "Convert MPEG to TXT Online for Free",
    displayTitle: "MPEG to TXT Converter",
    description:
      "Turn a 1-hour MPEG file into a TXT file in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert MPEG to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload MPEG File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert MPEG Files to TXT Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MPEG File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MPEG to Text", link: "/l/mpeg-to-text" },
      { text: "MPEG to Word", link: "/l/mpeg-to-word" },
      { text: "MPEG to PDF", link: "/l/mpeg-to-pdf" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WebM to TXT", link: "/l/webm-to-txt" },
      { text: "OGG to TXT", link: "/l/ogg-to-txt" },
    ],
  },
  "webm-to-txt": {
    title: "Convert WebM to TXT Online for Free",
    displayTitle: "WebM to TXT Converter",
    description:
      "Turn a 1-hour WebM file into a TXT file in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert WebM to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload WebM File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert WebM Files to TXT Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from WebM File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "WebM to PDF", link: "/l/webm-to-pdf" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "OGG to TXT", link: "/l/ogg-to-txt" },
      { text: "OPUS to TXT", link: "/l/opus-to-txt" },
      { text: "MPEG to TXT", link: "/l/mpeg-to-txt" },
    ],
  },
  "mov-to-text": {
    title: "Convert MOV to Text Online for Free",
    displayTitle: "MOV to Text Converter",
    description:
      "Turn a 1-hour MOV file into text in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert MOV to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload MOV File",
        step2: "Click Transcribe",
        step3: "Export Text Result",
      },
    },
    features: [
      { title: "Convert MOV Files to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MOV File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MOV to PDF", link: "/l/mov-to-pdf" },
      { text: "MOV to SRT", link: "/l/mov-to-srt" },
      { text: "MOV to Transcript", link: "/l/mov-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "MPEG to Text", link: "/l/mpeg-to-text" },
    ],
  },
  "mov-to-pdf": {
    title: "Convert MOV to PDF Online for Free",
    displayTitle: "MOV to PDF Converter",
    description:
      "Turn a 1-hour MOV file into a PDF document in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert MOV to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload MOV File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert MOV Files to PDF Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MOV File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MOV to Text", link: "/l/mov-to-text" },
      { text: "MOV to SRT", link: "/l/mov-to-srt" },
      { text: "MOV to Transcript", link: "/l/mov-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WebM to PDF", link: "/l/webm-to-pdf" },
      { text: "MPEG to PDF", link: "/l/mpeg-to-pdf" },
    ],
  },
  "mov-to-srt": {
    title: "Convert MOV to SRT Online for Free",
    displayTitle: "MOV to SRT Converter",
    description:
      "Turn a 1-hour MOV file into SRT subtitles in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert MOV to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Upload MOV File",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert MOV Files to SRT Subtitles in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MOV File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MOV to Text", link: "/l/mov-to-text" },
      { text: "MOV to PDF", link: "/l/mov-to-pdf" },
      { text: "MOV to Transcript", link: "/l/mov-to-transcript" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "AAC to SRT", link: "/l/aac-to-srt" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
    ],
  },
  "mov-to-transcript": {
    title: "Convert MOV to Transcript Online for Free",
    displayTitle: "MOV to Transcript Converter",
    description:
      "Turn a 1-hour MOV file into a transcript in under 1 minute powered by AI. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert MOV to Transcript in Three Easy Steps",
      stepTitles: {
        step1: "Upload MOV File",
        step2: "Click Transcribe",
        step3: "Export Transcript",
      },
    },
    features: [
      { title: "Convert MOV Files to Transcript in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from MOV File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "MOV to Text", link: "/l/mov-to-text" },
      { text: "MOV to PDF", link: "/l/mov-to-pdf" },
      { text: "MOV to SRT", link: "/l/mov-to-srt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "MPEG to Text", link: "/l/mpeg-to-text" },
    ],
  },
  "aac-to-text": {
    title: "Convert AAC to Text in Seconds for Free",
    displayTitle: "AAC to Text Converter Powered by AI",
    description:
      "Turn a 1-hour AAC file into text in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert AAC to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload AAC File",
        step2: "Click Transcribe",
        step3: "Export Text",
      },
    },
    features: [
      { title: "Convert AAC Files to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from AAC File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "AAC to SRT", link: "/l/aac-to-srt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "OGG to Text", link: "/l/ogg-to-text" },
      { text: "OPUS to Text", link: "/l/opus-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "MPEG to Text", link: "/l/mpeg-to-text" },
    ],
  },
  "aac-to-srt": {
    title: "Convert AAC to SRT in Seconds for Free",
    displayTitle: "AAC to SRT Converter Powered by AI",
    description:
      "Turn a 1-hour AAC file into SRT subtitles in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert AAC to SRT in Three Easy Steps",
      stepTitles: {
        step1: "Upload AAC File",
        step2: "Click Transcribe",
        step3: "Export as SRT",
      },
    },
    features: [
      { title: "Convert AAC Files to SRT Subtitles in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from AAC File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "AAC to Text", link: "/l/aac-to-text" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "MOV to SRT", link: "/l/mov-to-srt" },
      { text: "OGG to Text", link: "/l/ogg-to-text" },
      { text: "OPUS to Text", link: "/l/opus-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
    ],
  },
  "ogg-to-text": {
    title: "Convert OGG to Text in Seconds for Free",
    displayTitle: "OGG to Text Converter Powered by AI",
    description:
      "Turn a 1-hour OGG file into text in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert OGG to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload OGG File",
        step2: "Click Transcribe",
        step3: "Export Text",
      },
    },
    features: [
      { title: "Convert OGG Files to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from OGG File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "OGG to TXT", link: "/l/ogg-to-txt" },
      { text: "OGG to PDF", link: "/l/ogg-to-pdf" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "OPUS to Text", link: "/l/opus-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "MPEG to Text", link: "/l/mpeg-to-text" },
    ],
  },
  "ogg-to-txt": {
    title: "Convert OGG to TXT in Seconds for Free",
    displayTitle: "OGG to TXT Converter Powered by AI",
    description:
      "Turn a 1-hour OGG file into a TXT file in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert OGG to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload OGG File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
    features: [
      { title: "Convert OGG Files to TXT Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from OGG File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "OGG to Text", link: "/l/ogg-to-text" },
      { text: "OGG to PDF", link: "/l/ogg-to-pdf" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "OPUS to TXT", link: "/l/opus-to-txt" },
      { text: "WebM to TXT", link: "/l/webm-to-txt" },
      { text: "MPEG to TXT", link: "/l/mpeg-to-txt" },
    ],
  },
  "ogg-to-pdf": {
    title: "Convert OGG to PDF in Seconds for Free",
    displayTitle: "OGG to PDF Converter Powered by AI",
    description:
      "Turn a 1-hour OGG file into a PDF document in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert OGG to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload OGG File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
    features: [
      { title: "Convert OGG Files to PDF Documents in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from OGG File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "OGG to Text", link: "/l/ogg-to-text" },
      { text: "OGG to TXT", link: "/l/ogg-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "OPUS to PDF", link: "/l/opus-to-pdf" },
      { text: "WebM to PDF", link: "/l/webm-to-pdf" },
      { text: "MPEG to PDF", link: "/l/mpeg-to-pdf" },
    ],
  },
  "opus-to-text": {
    title: "Convert OPUS to Text in Seconds for Free",
    displayTitle: "OPUS to Text Converter Powered by AI",
    description:
      "Turn a 1-hour OPUS file into text in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert OPUS to Text in Three Easy Steps",
      stepTitles: {
        step1: "Upload OPUS File",
        step2: "Click Transcribe",
        step3: "Export Text",
      },
    },
    features: [
      { title: "Convert OPUS Files to Text in Seconds" },
      {
        title: "Generate Summaries, Mind Maps, and Key Insights from OPUS File",
      },
      {
        title: "Export Text in Various Formats (SRT, TXT, Word, PDF, CSV, VTT)",
      },
    ],
    converters: [
      { text: "OPUS to PDF", link: "/l/opus-to-pdf" },
      { text: "OPUS to TXT", link: "/l/opus-to-txt" },
      { text: "MP3 to Text", link: "/l/mp3-to-text" },
      { text: "MP4 to Text", link: "/l/mp4-to-text-converter" },
      { text: "WAV to Text", link: "/l/wav-to-text" },
      { text: "OGG to Text", link: "/l/ogg-to-text" },
      { text: "WebM to Text", link: "/l/webm-to-text" },
      { text: "MPEG to Text", link: "/l/mpeg-to-text" },
    ],
  },
  "opus-to-pdf": {
    title: "Convert OPUS to PDF in Seconds for Free",
    displayTitle: "OPUS to PDF Converter Powered by AI",
    description:
      "Turn a 1-hour OPUS file into a PDF document in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert OPUS to PDF in Three Easy Steps",
      stepTitles: {
        step1: "Upload OPUS File",
        step2: "Click Transcribe",
        step3: "Export as PDF",
      },
    },
  },
  "opus-to-txt": {
    title: "Convert OPUS to TXT in Seconds for Free",
    displayTitle: "OPUS to TXT Converter Powered by AI",
    description:
      "Turn a 1-hour OPUS file into a TXT file in under 1 minute. Support for multiple formats and easy export options make transcription a breeze. Try it now!",
    howToUse: {
      title: "Convert OPUS to TXT in Three Easy Steps",
      stepTitles: {
        step1: "Upload OPUS File",
        step2: "Click Transcribe",
        step3: "Export as TXT",
      },
    },
  },
};
