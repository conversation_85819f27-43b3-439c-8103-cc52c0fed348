{"loading": "Betöltés...", "meta": {"title": "Ingyenes YouTube Átirat <PERSON> | Generálja és Töltse le a YouTube Átiratokat", "description": "Készítsen videóátiratokat egy kattintással. Töltse le a feliratokat több formátumban (SRT, VTT, TXT, CSV). Regisztráció nem szükséges, gyors és biztonságos.", "ogTitle": "YouTube Átirat Generátor | Videó Átiratok Kinyerése és Letöltése Ingyen", "ogDescription": "Generáljon YouTube átiratokat azonnal ingyen. Kinyerheti a videó átiratait egyetlen kattintással. Töltse le a feliratokat több formátumban (SRT, VTT, TXT, CSV). Regisztráció nem szükséges, gyors és biztonságos.", "ogImageAlt": "UniScribe YouTube <PERSON><PERSON><PERSON>", "twitterTitle": "YouTube Átirat Generátor | Videó Átiratok Kinyerése és Letöltése Ingyen", "twitterDescription": "Könnyedén kinyerheti a YouTube videók átiratait. Töltse le a feliratokat több formátumban (SRT, VTT, TXT, CSV). Regisztráció nem szükséges, gyors és biztonságos.", "schemaTitle": "YouTube <PERSON><PERSON><PERSON>", "schemaDescription": "Könnyedén kinyerheti a YouTube videók átiratait. Töltse le a feliratokat több formátumban (SRT, VTT, TXT, CSV). Regisztráció nem szükséges, gyors és biztonságos."}, "hero": {"title": "Ingyenes YouTube Átiratok Kinyerése", "description": "Könnyedén konvertálhat egy YouTube videót átirattá, másolja és töltse le a létrehozott YouTube átiratot egy kattintás<PERSON>."}, "features": {"extraction": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Teljes videóátiratok kinyerése időbélyegekkel"}, "formats": {"title": "Több exportálási formátum", "description": "Töltse le TXT, SRT, VTT és CSV formátumban"}, "instant": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> azonnal", "description": "Szerezze meg a transzkripteket azonnal regisztráció <PERSON>"}}, "buttons": {"getTranscript": "Szerezze be a jegyzőkönyvet", "goToTranscribe": "Menj a Transcribe-hoz", "export": "Exportálás"}, "preview": {"title": "<PERSON><PERSON><PERSON>"}, "transcript": {"title": "<PERSON><PERSON><PERSON>", "includeTimestamps": "Időbélyegek hozzáadása"}, "faq": {"what": {"question": "Mi az a YouTube Transcript Extractor?", "answer": "A YouTube Átirat <PERSON> egy ingyenes es<PERSON>öz, amely le<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy <PERSON><PERSON><PERSON> vonjon ki és töltsön le YouTube videókból."}, "howTo": {"question": "<PERSON><PERSON><PERSON> a YouTube Átirat Kivonót?", "answer": "Egyszerűen illessze be a YouTube videó URL-jét, é<PERSON> kattintson a 'Get Transcript' gombra."}, "free": {"question": "A YouTube Transcript Extractor ingyenesen használható?", "answer": "<PERSON><PERSON>, az alapvető <PERSON>tiratkinyerési funkció teljesen ingyenes."}, "formats": {"question": "Letölthetem a YouTube videók átiratait különböző formátumokban?", "answer": "<PERSON><PERSON>, let<PERSON>ltheti a transzkripciókat .txt, .srt, .vtt és .csv formátumban."}, "private": {"question": "Kivonhatok átiratokat privát YouTube videókból?", "answer": "<PERSON><PERSON>, a transzkripek csak nyilvános videókból vonhatók ki."}, "limit": {"question": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>, hogy h<PERSON>y <PERSON> tudok kinyer<PERSON>?", "answer": "<PERSON><PERSON>, nincs k<PERSON> a kinyerhető átiratok számában."}, "account": {"question": "Szükséges fiókot létrehoznom a YouTube Transcript Extractor használatához?", "answer": "Az alapvető átirat megtekintése ingyenes fiók nélkül. Azonban az olyan funkciók, mint a feliratok másolása és letöltése ingyenes fiók bejelentkezést igényelnek."}, "extract": {"question": "Kihúzhatom a YouTube videók átiratait?", "answer": "Ha a videónak már léte<PERSON>, azo<PERSON> közvetlenül ki tudjuk nyerni. Ha nem, használhatja a Uniscribe YouTube átirat készítő funkcióját az átirat létrehozásához."}}, "upgradeNotice": "Elnézést a megszakításért! A YouTube átirat frissítés al<PERSON>, és hamarosan vissza<PERSON>r."}