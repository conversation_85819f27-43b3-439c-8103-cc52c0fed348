"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import FAQ from "@/components/Common/FAQ";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toolsService } from "@/services/api/toolsService";
import Loading from "@/components/Dashboard/Loading";
import { handleFileDownload } from "@/lib/downloadHelper";
import { trackEvent } from "@/lib/analytics";
import CopyButton from "@/components/Common/CopyButton";
import RecommendedTools from "@/components/Common/RecommendedTools";
import { FeaturesGrid } from "@/components/Common/FeaturesGrid";
import { Zap, Download, FileText } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/navigation";
import isValidYouTubeUrl from "@/lib/youtube";
import { FEATURES } from "@/config/features";

export default function YoutubeTranscriptExtractor() {
  const t = useTranslations("tools.youtubeTranscript");
  const router = useRouter();
  const [url, setUrl] = useState("");
  const [videoId, setVideoId] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [subtitles, setSubtitles] = useState([]);
  const [selectedSubtitle, setSelectedSubtitle] = useState(null);
  const [error, setError] = useState(null);
  const [selectedFormat, setSelectedFormat] = useState("txt");
  const [includeTimestamp, setIncludeTimestamp] = useState(true);

  const features = [
    {
      icon: FileText,
      title: t("features.extraction.title"),
      description: t("features.extraction.description"),
    },
    {
      icon: Download,
      title: t("features.formats.title"),
      description: t("features.formats.description"),
    },
    {
      icon: Zap,
      title: t("features.instant.title"),
      description: t("features.instant.description"),
    },
  ];
  const faqs = [
    {
      question: t("faq.what.question"),
      answer: t("faq.what.answer"),
    },
    {
      question: t("faq.howTo.question"),
      answer: t("faq.howTo.answer"),
    },
    {
      question: t("faq.free.question"),
      answer: t("faq.free.answer"),
    },
    {
      question: t("faq.formats.question"),
      answer: t("faq.formats.answer"),
    },
    {
      question: t("faq.private.question"),
      answer: t("faq.private.answer"),
    },
    {
      question: t("faq.limit.question"),
      answer: t("faq.limit.answer"),
    },
    {
      question: t("faq.account.question"),
      answer: t("faq.account.answer"),
    },
    {
      question: t("faq.extract.question"),
      answer: t("faq.extract.answer"),
    },
  ];

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    // 检查YouTube升级状态
    if (FEATURES.YOUTUBE_UPGRADE.enabled) {
      setError({
        message: t("upgradeNotice"),
        showTranscribeButton: false,
      });
      setIsLoading(false);
      return;
    }

    // 添加 URL 验证
    const validation = isValidYouTubeUrl(url);
    if (!validation.isValid) {
      setError({
        message: `Invalid YouTube URL: ${validation.reason}`,
        showTranscribeButton: false,
      });
      setIsLoading(false);

      //仅当前端验证searchUrl不满足任何已知YouTube url 模式时，才记录错误
      //其他要么是本来就不是YouTube链接，要么就是后端返回的视频权限之类的问题，没有必要在前端打点上报
      if (validation.reason === "Invalid YouTube video URL") {
        trackEvent("youtube_search_error", {
          url: url,
          error: "Invalid YouTube video URL",
          source: "youtube_transcript_extractor",
        });
      }

      return;
    }

    // 追踪获取字幕的点击事件
    trackEvent("youtube_get_transcript_click", {});

    const extractedVideoId = validation.videoId;
    try {
      if (!extractedVideoId) {
        throw { data: { message: "Invalid YouTube URL" } };
      }

      setVideoId(extractedVideoId);
      // Use the extracted URL if available
      const urlToUse = validation.extractedUrl || url;
      const data = await toolsService.getYoutubeSubtitleList(urlToUse);

      const formattedSubtitles = data.subtitles.map((subtitle) => ({
        code: subtitle.langCode,
        label: subtitle.name,
        content: parseVTTContent(subtitle.vttContent),
        formats: subtitle.availableFormats,
      }));

      setSubtitles(formattedSubtitles);
      if (formattedSubtitles.length > 0) {
        setSelectedSubtitle(formattedSubtitles[0]);
      }

      // 追踪获取字幕成功
      trackEvent("youtube_get_transcript_success", {
        videoId: extractedVideoId,
        subtitleCount: formattedSubtitles.length,
      });
    } catch (error) {
      console.error("Error fetching subtitles:", error);

      // 追踪获取字幕失败
      trackEvent("youtube_get_transcript_error", {
        url: url,
        error: error.data?.message || "Unknown error",
      });

      setSubtitles([]);
      setSelectedSubtitle(null);

      // 特殊处理无字幕的情况
      if (error.data?.code === 40002) {
        setError({
          message: error.data.message,
          suggestion:
            "You can use Uniscribe's YouTube transcription feature to generate subtitles",
          showTranscribeButton: true,
        });
      } else {
        setError({
          message: error.data?.message || "An error occurred",
          showTranscribeButton: false,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const parseVTTContent = (vttContent) => {
    const lines = vttContent.split("\n");
    const content = [];
    let currentItem = {};

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.includes("-->")) {
        const [timeStr] = line.split("-->")[0].trim().split(".");
        const [hours, minutes, seconds] = timeStr.split(":").map(Number);
        const totalSeconds = hours * 3600 + minutes * 60 + seconds;

        currentItem = {
          start: totalSeconds,
          text: lines[i + 1]?.trim() || "",
          duration: 2,
        };
        content.push(currentItem);
        i++; // 跳过字幕文本行
      }
    }

    return content;
  };

  const handleExport = async () => {
    if (!selectedSubtitle) return;

    trackEvent("youtube_subtitle_export_click", {
      language: selectedSubtitle.code,
      format: selectedFormat,
      includeTimestamp,
    });

    try {
      // Re-validate the URL to extract the video ID
      const validation = isValidYouTubeUrl(url);
      let extractedVideoId;

      if (validation.isValid) {
        extractedVideoId = validation.videoId;
      } else {
        // Fallback to traditional extraction
        extractedVideoId = new URL(url).searchParams.get("v");
      }

      if (!extractedVideoId) {
        throw new Error("Invalid YouTube URL");
      }

      const response = await toolsService.downloadYoutubeSubtitle(
        extractedVideoId,
        selectedSubtitle.code,
        selectedFormat,
        includeTimestamp
      );

      if (response.status === 200) {
        handleFileDownload(
          response.data,
          response.headers,
          `subtitle.${selectedFormat}`
        );

        // 追踪导出成功
        trackEvent("youtube_subtitle_export_success", {
          videoId: extractedVideoId,
          language: selectedSubtitle.code,
          format: selectedFormat,
        });
      } else {
        throw new Error("Failed to download subtitle");
      }
    } catch (error) {
      console.error("Error downloading subtitle:", error);

      // 追踪导出失败
      trackEvent("youtube_subtitle_export_error", {
        videoId: extractedVideoId,
        language: selectedSubtitle?.code,
        format: selectedFormat,
        error: error.message,
      });

      setError({
        message: "Failed to download subtitle",
        showTranscribeButton: false,
      });
    }
  };

  const handleGoToTranscribe = () => {
    // 追踪跳转到转录页面的点击事件
    trackEvent("youtube_go_to_transcribe_click", {
      fromUrl: url,
      videoId: videoId,
      reason: "no_subtitles",
    });

    router.push("/dashboard");
  };

  const handleCopyTranscript = () => {
    if (!selectedSubtitle) return;

    trackEvent("youtube_subtitle_copy_click", {
      videoId: videoId,
      language: selectedSubtitle.code,
      includeTimestamp,
    });

    const transcriptText = selectedSubtitle.content
      .map((item) =>
        includeTimestamp
          ? `${new Date(item.start * 1000).toISOString().substr(11, 8)} ${
              item.text
            }`
          : item.text
      )
      .join(includeTimestamp ? "\n" : " ");

    return transcriptText;
  };

  // When you need the selected language
  const selectedLanguage = selectedSubtitle?.code || "";

  return (
    <main className="min-h-screen bg-background">
      {/* 上部分保持 max-w-3xl */}
      <div className="max-w-4xl mx-auto space-y-6 px-4 sm:px-6  pt-6">
        {/* Hero Section */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold mb-4 md:text-5xl lg:text-6xl text-custom-bg">
            {t("hero.title")}
          </h1>
          <h2 className="text-muted-foreground mb-8 max-w-2xl mx-auto text-lg font-normal">
            {t("hero.description")}
          </h2>

          <FeaturesGrid features={features} />

          {/* Input Form */}
          <form onSubmit={handleSubmit} className="max-w-4xl mx-auto pt-8">
            <div className="flex gap-2">
              <Input
                type="url"
                placeholder="https://www.youtube.com/watch?v=7k1ehaE0bdU"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                onFocus={(e) => (e.target.placeholder = "")}
                onBlur={(e) =>
                  (e.target.placeholder =
                    "https://www.youtube.com/watch?v=7k1ehaE0bdU")
                }
                className="flex-1 placeholder:text-gray-300 focus:outline-none focus-visible:border-custom-bg focus-visible:ring-0"
              />
              <Button
                type="submit"
                className="bg-custom-bg hover:bg-custom-bg/90"
              >
                {t("buttons.getTranscript")}
              </Button>
            </div>
            {error && !videoId && (
              <div
                className={`mt-2 text-sm ${
                  error.message === t("upgradeNotice")
                    ? "text-orange-600"
                    : "text-destructive"
                }`}
              >
                {error.message}
              </div>
            )}
          </form>
        </div>
      </div>

      {/* 视频预览和字幕部分使用更宽的容器 */}
      {videoId && (
        <div className="max-w-6xl mx-auto px-4 sm:px-6 pt-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardContent className="p-4">
                <h2 className="text-xl font-semibold mb-4">
                  {t("preview.title")}
                </h2>
                <div className="aspect-video">
                  <iframe
                    width="100%"
                    height="100%"
                    src={`https://www.youtube.com/embed/${videoId}?enablejsapi=1`}
                    allowFullScreen
                    className="rounded-lg"
                  ></iframe>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h2 className="text-xl font-semibold mb-4">
                  {t("transcript.title")}
                </h2>
                {error ? (
                  <div className="p-4 bg-destructive/10 text-destructive rounded-lg">
                    <p>{error.message}</p>
                    {error.suggestion && (
                      <p className="mt-2 text-sm">{error.suggestion}</p>
                    )}
                    {error.showTranscribeButton && (
                      <Button
                        onClick={handleGoToTranscribe}
                        variant="outline"
                        className="mt-4 text-white bg-custom-bg hover:bg-custom-bg/90"
                      >
                        {t("buttons.goToTranscribe")}
                      </Button>
                    )}
                  </div>
                ) : isLoading ? (
                  <div className="flex items-center justify-center h-[calc(100vh-400px)]">
                    <Loading />
                  </div>
                ) : (
                  <>
                    <Tabs
                      value={selectedSubtitle?.code}
                      defaultValue={selectedSubtitle?.code}
                      className="mb-4"
                    >
                      <TabsList className="mb-4">
                        {subtitles.map((subtitle) => (
                          <TabsTrigger
                            key={subtitle.code}
                            value={subtitle.code}
                            onClick={() => setSelectedSubtitle(subtitle)}
                          >
                            {subtitle.label}
                          </TabsTrigger>
                        ))}
                      </TabsList>
                      {subtitles.map((subtitle) => (
                        <TabsContent key={subtitle.code} value={subtitle.code}>
                          <div className="h-[calc(100vh-400px)] overflow-y-auto bg-white p-4 rounded-lg">
                            {subtitle.content.map((item, index) => (
                              <div
                                key={index}
                                className="group mb-2 p-2 rounded cursor-pointer transition-colors duration-200 hover:bg-gray-50"
                              >
                                <span className="text-muted-foreground mr-2 text-sm leading-relaxed font-light">
                                  {new Date(item.start * 1000)
                                    .toISOString()
                                    .substr(11, 8)}
                                </span>
                                <span className="text-sm leading-relaxed font-light">
                                  {item.text}
                                </span>
                              </div>
                            ))}
                          </div>
                        </TabsContent>
                      ))}
                    </Tabs>
                    {selectedSubtitle && (
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2 mr-4">
                          <input
                            type="checkbox"
                            id="includeTimestamp"
                            checked={includeTimestamp}
                            onChange={(e) =>
                              setIncludeTimestamp(e.target.checked)
                            }
                            className="rounded border-gray-300 text-custom-bg focus:ring-custom-bg"
                          />
                          <label
                            htmlFor="includeTimestamp"
                            className="text-sm text-muted-foreground"
                          >
                            {t("transcript.includeTimestamps")}
                          </label>
                        </div>
                        <CopyButton
                          content={handleCopyTranscript()}
                          onCopySuccess={() => {
                            trackEvent("youtube_subtitle_copy_success", {
                              videoId: videoId,
                              language: selectedSubtitle?.code,
                              includeTimestamp,
                            });
                          }}
                          onCopyError={(error) => {
                            trackEvent("youtube_subtitle_copy_error", {
                              videoId: videoId,
                              language: selectedSubtitle?.code,
                              includeTimestamp,
                              error: error.message,
                            });
                          }}
                        />
                        <Select
                          onValueChange={setSelectedFormat}
                          value={selectedFormat}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue placeholder="Export as" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="srt">SRT</SelectItem>
                            <SelectItem value="vtt">VTT</SelectItem>
                            <SelectItem value="csv">CSV</SelectItem>
                            <SelectItem value="txt">TXT</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button
                          onClick={handleExport}
                          className="bg-custom-bg hover:bg-custom-bg/90"
                        >
                          {t("buttons.export")}
                        </Button>
                      </div>
                    )}
                  </>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* 底部内容继续使用 max-w-3xl */}
      <div className="max-w-3xl mx-auto space-y-6 px-4 sm:px-6 pt-16">
        <RecommendedTools />
        <FAQ faqs={faqs} />
      </div>
    </main>
  );
}
