"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Logo from "@/components/Logo";
import { useAuthStore } from "@/stores/useAuthStore";
import { useBanner } from "@/contexts/BannerContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Menu, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { Link } from "@/components/Common/Link";
import LanguageSwitcher from "@/components/LanguageSwitcher";
import { useState } from "react";

const NavBar = () => {
  const { isBannerVisible } = useBanner();
  const { user, loading } = useAuthStore();
  const isLoggedIn = !!user && !user.isAnonymous;
  const t = useTranslations("navbar");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isToolsExpanded, setIsToolsExpanded] = useState(false);

  const toolLinks = [
    {
      href: "/tools/video-to-audio-extractor",
      label: "toolsList.videoToAudio",
    },
    {
      href: "/tools/wav-to-mp3-converter",
      label: "toolsList.wavToMp3",
    },
  ];

  const handleMobileMenuToggle = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleToolsToggle = () => {
    setIsToolsExpanded(!isToolsExpanded);
  };

  const handleToolItemClick = () => {
    setIsMobileMenuOpen(false);
    setIsToolsExpanded(false);
  };

  const handleMenuItemClick = () => {
    setIsMobileMenuOpen(false);
    setIsToolsExpanded(false);
  };

  return (
    <header
      className={`bg-white/80 backdrop-blur-md fixed ${
        isBannerVisible ? "top-[52px]" : "top-0"
      } left-0 right-0 z-50 shadow-sm`}
    >
      <div className="container mx-auto px-4 py-4 flex justify-between items-center relative">
        <div>
          <Logo />
        </div>
        <nav className="hidden md:flex space-x-4">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="text-base flex items-center gap-1"
              >
                {t("tools")}
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="start" className="w-[280px]">
              {toolLinks.map((link) => (
                <DropdownMenuItem key={link.href} asChild>
                  <Link
                    href={link.href}
                    className="w-full py-3 px-4 text-base whitespace-nowrap"
                  >
                    {t(link.label)}
                  </Link>
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <Link href="/#features">
            <Button variant="ghost" className="text-base">
              {t("features")}
            </Button>
          </Link>
          <Link href="/?utm_source=navbar#subscription-price">
            <Button variant="ghost" className="text-base">
              {t("pricing")}
            </Button>
          </Link>
          <Link href="/#faq">
            <Button variant="ghost" className="text-base">
              {t("faq")}
            </Button>
          </Link>
          <Link href="/blog">
            <Button variant="ghost" className="text-base">
              {t("blog")}
            </Button>
          </Link>
          <Link
            href="https://trello.com/b/lHLwNeS8/uniscribe-roadmap"
            target="_blank"
            rel="noopener noreferrer"
          >
            <Button variant="ghost" className="text-base">
              {t("roadmap")}
            </Button>
          </Link>
        </nav>
        <div className="hidden md:flex items-center space-x-4">
          <LanguageSwitcher isMobile={false} />
          {isLoggedIn ? (
            <Link href="/dashboard">
              <Button
                variant="ghost"
                className="text-base text-custom-bg border border-custom-bg bg-white hover:text-custom-bg-600 focus:text-custom-bg-700"
              >
                {t("dashboard")}
              </Button>
            </Link>
          ) : (
            <Link href="/auth/signin">
              <Button
                variant="ghost"
                className="text-sm text-custom-bg border border-custom-bg bg-white hover:text-custom-bg-600 focus:text-custom-bg-700"
              >
                {t("signIn")}
              </Button>
            </Link>
          )}
          <Link href={isLoggedIn ? "/dashboard" : "/auth/signin"}>
            <Button className="text-sm bg-custom-bg hover:bg-custom-bg-500 focus:bg-custom-bg-600">
              {t("startFree")}
            </Button>
          </Link>
        </div>

        <button
          className="md:hidden p-2"
          onClick={handleMobileMenuToggle}
          aria-label={isMobileMenuOpen ? "menu close" : "menu open"}
        >
          {isMobileMenuOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      </div>

      {isMobileMenuOpen && (
        <div className="md:hidden bg-white absolute left-0 right-0 top-full">
          <div className="h-[100dvh] overflow-y-auto">
            <div className="px-4 py-2 space-y-2">
              <div>
                <Button
                  variant="ghost"
                  className="w-full justify-between text-base"
                  onClick={handleToolsToggle}
                >
                  {t("tools")}
                  <ChevronDown
                    className={`h-4 w-4 transition-transform duration-200 ${
                      isToolsExpanded ? "rotate-180" : ""
                    }`}
                  />
                </Button>

                {isToolsExpanded && (
                  <div className="pl-4 space-y-2 mt-2">
                    {toolLinks.map((link) => (
                      <Link
                        key={link.href}
                        href={link.href}
                        className="block w-full"
                        onClick={handleMenuItemClick}
                      >
                        <Button
                          variant="ghost"
                          className="w-full text-base justify-start"
                        >
                          {t(link.label)}
                        </Button>
                      </Link>
                    ))}
                  </div>
                )}
              </div>

              <Link
                href="/#features"
                className="block"
                onClick={handleMenuItemClick}
              >
                <Button
                  variant="ghost"
                  className="w-full text-base justify-start"
                >
                  {t("features")}
                </Button>
              </Link>
              <Link
                href="/?utm_source=navbar#subscription-price"
                className="block"
                onClick={handleMenuItemClick}
              >
                <Button
                  variant="ghost"
                  className="w-full text-base justify-start"
                >
                  {t("pricing")}
                </Button>
              </Link>
              <Link
                href="/#faq"
                className="block"
                onClick={handleMenuItemClick}
              >
                <Button
                  variant="ghost"
                  className="w-full text-base justify-start"
                >
                  {t("faq")}
                </Button>
              </Link>
              <Link
                href="/blog"
                className="block"
                onClick={handleMenuItemClick}
              >
                <Button
                  variant="ghost"
                  className="w-full text-base justify-start"
                >
                  {t("blog")}
                </Button>
              </Link>
              <Link
                href="https://trello.com/b/lHLwNeS8/uniscribe-roadmap"
                className="block"
                onClick={handleMenuItemClick}
                target="_blank"
                rel="noopener noreferrer"
              >
                <Button
                  variant="ghost"
                  className="w-full text-base justify-start"
                >
                  {t("roadmap")}
                </Button>
              </Link>

              <div className="pt-2 space-y-2">
                <LanguageSwitcher isMobile={true} />
                {isLoggedIn ? (
                  <Link
                    href="/dashboard"
                    className="block"
                    onClick={handleMenuItemClick}
                  >
                    <Button
                      variant="ghost"
                      className="w-full text-base text-custom-bg border border-custom-bg bg-white"
                    >
                      {t("dashboard")}
                    </Button>
                  </Link>
                ) : (
                  <Link
                    href="/auth/signin"
                    className="block"
                    onClick={handleMenuItemClick}
                  >
                    <Button
                      variant="ghost"
                      className="w-full text-base text-custom-bg border border-custom-bg bg-white"
                    >
                      {t("signIn")}
                    </Button>
                  </Link>
                )}
                <Link
                  href={isLoggedIn ? "/dashboard" : "/auth/signin"}
                  className="block"
                  onClick={handleMenuItemClick}
                >
                  <Button className="w-full text-base bg-custom-bg">
                    {t("startFree")}
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default NavBar;
