"use client";
import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { Check, Flame } from "lucide-react";
import { useTranslations } from "next-intl";
import { useAuthStore } from "@/stores/useAuthStore";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { subscriptionService } from "@/services";
import { trackEvent } from "@/lib/analytics";
import { useRouter } from "@/i18n/navigation";
import { safeOpenUrl } from "@/lib/browserUtils";
import PriceIncreaseCountdown from "./PriceIncreaseCountdown";
import { FEATURES } from "@/config/features";
import { FREE_USER_LIMITS } from "@/constants/file";

// Feature rendering component
const FeatureItem = ({ feature }) => {
  if (typeof feature === "string") {
    return feature;
  }

  return (
    <>
      {feature.hyperlink ? (
        <a
          href={feature.hyperlink}
          target="_blank"
          rel="noopener noreferrer"
          className="text-current underline hover:no-underline cursor-pointer"
        >
          {feature.name}
        </a>
      ) : (
        feature.name
      )}
      {feature.tag && (
        <span className="inline-flex items-center whitespace-nowrap rounded-full bg-custom-bg/10 px-2 py-0.5 text-xs font-medium text-custom-bg">
          {feature.tag}
        </span>
      )}
    </>
  );
};

const Pricing = ({
  showFreeTier = true,
  title,
  description,
  defaultPlanType = "yearly",
  titleMarginTop = "mt-16",
  containerClassName = "",
  source = "pricing",
}) => {
  const t = useTranslations("home.pricing");
  const searchParams = useSearchParams();

  // 从 URL 参数中获取初始计划类型
  const planTypeFromQuery = searchParams.get("plantype");
  const initialPlanType =
    planTypeFromQuery &&
    ["yearly", "monthly", "onetime"].includes(planTypeFromQuery)
      ? planTypeFromQuery
      : defaultPlanType;

  const [activeTier, setActiveTier] = useState("basic");
  const [activePlanType, setActivePlanType] = useState(initialPlanType);

  // 只在组件初始化时从 URL 参数读取计划类型
  // 使用空依赖数组确保只执行一次
  useEffect(() => {
    // 初始化时已经在 useState 中设置了初始值
    // 不需要额外的操作
  }, []);

  // 检查价格增长倒计时是否已经结束
  const isPriceIncreaseEnded = () => {
    if (!FEATURES.PRICE_INCREASE.enabled) return false;
    const now = new Date();
    const endDate = new Date(FEATURES.PRICE_INCREASE.endDate);
    return now >= endDate;
  };

  // 统一价格配置
  const plansConfig = {
    basic: {
      type: "subscription",
      monthly: 10,
      yearlyDiscount: 0.4,
      minutes: 1200,
      planIds: {
        monthly: "basic_monthly",
        yearly: "basic_yearly",
      },
    },
    pro: {
      type: "subscription",
      monthly: 30,
      yearlyDiscount: 0.4,
      minutes: 6000,
      planIds: {
        monthly: "pro_monthly",
        yearly: "pro_yearly",
      },
    },
    lite: {
      type: "onetime",
      original: 9.9,
      discounted: 9.9,
      minutes: 300,
      planId: "lite_one_time",
    },
    plus: {
      type: "onetime",
      original: 49.9,
      discounted: 24.9,
      minutes: 2400,
      planId: "plus_one_time",
    },
    max: {
      type: "onetime",
      original: 79.9,
      discounted: 39.9,
      minutes: 6000,
      planId: "max_one_time",
    },
  };

  const [loadingTier, setLoadingTier] = useState(null);
  const router = useRouter();
  const { user } = useAuthStore();
  const isLoggedIn = !!user && !user.isAnonymous;
  const hasActiveSubscription = user?.hasActiveSubscription;

  // 修改辅助函数来确定计划 ID，直接从 plansConfig 获取
  const getPlanId = (tier, planType) => {
    const tierName = tier.canonicalName;

    // 处理一次性付款计划
    if (tier.type === "onetime") {
      return plansConfig[tierName].planId;
    }

    // 处理订阅计划
    const tierConfig = plansConfig[tierName.toLowerCase()];
    if (tierConfig && tierConfig.planIds) {
      return tierConfig.planIds[planType];
    }

    return null;
  };

  const handleSubscribe = async (tier) => {
    setLoadingTier(tier.canonicalName.toLowerCase());

    // 添加打点记录
    trackEvent("pricing_subscribe_click", {
      tier: tier.canonicalName,
      subscription_type: activePlanType,
      isLoggedIn: isLoggedIn,
    });

    try {
      // 确定支付模式
      const mode = tier.type === "onetime" ? "payment" : "subscription";

      // 使用辅助函数获取计划 ID
      const planId = getPlanId(tier, activePlanType);

      if (!isLoggedIn) {
        // 保存订阅意图信息到 localStorage
        const subscriptionIntent = {
          tier: tier.canonicalName,
          subscriptionType: activePlanType,
          planId: planId,
          mode: mode,
          timestamp: new Date().getTime(),
        };

        console.log(
          "💾 [Pricing] Saving subscription intent:",
          subscriptionIntent
        );

        // 将信息存储在 localStorage 中
        localStorage.setItem(
          "subscriptionIntent",
          JSON.stringify(subscriptionIntent)
        );

        console.log("🔄 [Pricing] Redirecting to signin page");
        // 重定向到登录页面
        router.push("/auth/signin");
        return;
      }

      // 如果用户已有订阅，且点击的是订阅类型的按钮，跳转到客户门户
      if (hasActiveSubscription && tier.type === "subscription") {
        const response = await subscriptionService.createCustomerPortal();
        safeOpenUrl(response.data.url);
        return;
      }

      // 如果没有有效的计划 ID，重定向到仪表盘
      if (!planId) {
        router.push("/dashboard");
        return;
      }

      const response = await subscriptionService.createCheckoutSession(
        planId,
        null,
        mode,
        source
      );

      // 添加 umami 收入追踪
      if (response.status === 200) {
        // 构造具体的事件名称用于收入报告分组
        const eventName =
          tier.type === "onetime"
            ? `revenue_onetime_${tier.canonicalName}`
            : `revenue_${tier.canonicalName}_${activePlanType}`;

        const revenue =
          tier.type === "onetime"
            ? tier.discountedPrice
            : activePlanType === "yearly"
              ? parseFloat(tier.yearlyTotalPrice.replace("$", ""))
              : parseFloat(tier.monthlyPrice.replace("$", ""));

        trackEvent(eventName, {
          revenue: revenue,
          currency: "USD",
        });
      }

      safeOpenUrl(response.data.url);
    } catch (error) {
      console.error(error);
    } finally {
      setLoadingTier(null);
    }
  };

  const baseTiers = [
    {
      canonicalName: "free",
      name: t("tiers.free.name"),
      type: "free",
      monthlyPrice: "$0",
      yearlyPrice: "$0",
      yearlyTotalPrice: "$0",
      minutes: 120,
      description: t("tiers.free.description"),
      features: [
        t("features.transcriptionMinutes", { minutes: 120 }),
        t("features.dailyFileLimit", {
          count: FREE_USER_LIMITS.DAILY_FILE_COUNT,
        }),
        t("features.standardModel"),
        t("features.languages"),
        t("features.basicExport"),
        {
          name: t("features.textSummary"),
          tag: t("tags.limitedOffer"),
        },
        {
          name: t("features.mindMap"),
          tag: t("tags.limitedOffer"),
        },
        {
          name: t("features.qaExtraction"),
          tag: t("tags.limitedOffer"),
        },
        {
          name: t("features.youtubeTranscription"),
          tag: t("tags.limitedOffer"),
        },
        t("features.retention"),
        t("features.emailSupport"),
      ],
      cta: t("tiers.free.cta"),
    },
    {
      canonicalName: "basic",
      name: t("tiers.basic.name"),
      type: "subscription",
      isPopular: true,
      monthlyPrice: `$${plansConfig.basic.monthly}`,
      yearlyPrice: `$${
        plansConfig.basic.monthly * (1 - plansConfig.basic.yearlyDiscount)
      }`,
      yearlyTotalPrice: `$${
        plansConfig.basic.monthly * (1 - plansConfig.basic.yearlyDiscount) * 12
      }`,
      minutes: plansConfig.basic.minutes,
      description: t("tiers.basic.description"),
      features: [
        t("features.transcriptionMinutes", {
          minutes: plansConfig.basic.minutes,
        }),
        t("features.noFileLimit"),
        t("features.premiumModel"),
        t("features.languages"),
        t("features.advancedExport"),
        t("features.textSummary"),
        t("features.mindMap"),
        t("features.qaExtraction"),
        t("features.youtubeTranscription"),
        { name: t("features.speakerIdentification"), tag: t("tags.new") },
        {
          name: t("features.apiAccess"),
          tag: t("tags.new"),
          hyperlink: "/docs",
        },
        t("features.noRetention"),
        t("features.prioritySupport"),
      ],
      cta: t("tiers.basic.cta"),
      planIds: plansConfig.basic.planIds,
    },
    {
      canonicalName: "pro",
      name: t("tiers.pro.name"),
      type: "subscription",
      monthlyPrice: `$${plansConfig.pro.monthly}`,
      yearlyPrice: `$${
        plansConfig.pro.monthly * (1 - plansConfig.pro.yearlyDiscount)
      }`,
      yearlyTotalPrice: `$${
        plansConfig.pro.monthly * (1 - plansConfig.pro.yearlyDiscount) * 12
      }`,
      minutes: plansConfig.pro.minutes,
      description: t("tiers.pro.description"),
      features: [
        t("features.transcriptionMinutes", {
          minutes: plansConfig.pro.minutes,
        }),
        t("features.noFileLimit"),
        t("features.premiumModel"),
        t("features.languages"),
        t("features.advancedExport"),
        t("features.textSummary"),
        t("features.mindMap"),
        t("features.qaExtraction"),
        t("features.youtubeTranscription"),
        { name: t("features.speakerIdentification"), tag: t("tags.new") },
        {
          name: t("features.apiAccess"),
          tag: t("tags.new"),
          hyperlink: "/docs",
        },
        t("features.noRetention"),
        t("features.prioritySupport"),
      ],
      cta: t("tiers.pro.cta"),
      planIds: plansConfig.pro.planIds,
    },
  ];

  // 修改 oneTimeTiers 配置
  const oneTimeTiers = ["lite", "plus", "max"].map((planKey) => ({
    canonicalName: planKey,
    name: t(`tiers.${planKey}.name`),
    type: "onetime",
    planId: plansConfig[planKey].planId,
    originalPrice: plansConfig[planKey].original,
    discountedPrice: plansConfig[planKey].discounted,
    minutes: plansConfig[planKey].minutes,
    description: t(`tiers.${planKey}.description`),
    features: [
      t("features.transcriptionMinutesOnetime", {
        minutes: plansConfig[planKey].minutes,
      }),
      t("features.validity"),
      t("features.noFileLimit"),
      t("features.premiumModel"),
      t("features.languages"),
      t("features.advancedExport"),
      t("features.textSummary"),
      t("features.mindMap"),
      t("features.qaExtraction"),
      t("features.youtubeTranscription"),
      { name: t("features.speakerIdentification"), tag: t("tags.new") },
      t("features.noRetention"),
      t("features.prioritySupport"),
    ],
    cta: t(`tiers.${planKey}.cta`),
  }));

  // 合并所有套餐
  const allTiers = [
    ...(showFreeTier ? [baseTiers[0]] : []),
    ...baseTiers.slice(1),
    ...oneTimeTiers,
  ];

  return (
    <div className={`w-full ${containerClassName}`}>
      <div className="container mx-auto max-w-[1400px]">
        <h2
          className={`text-4xl font-bold text-center mb-6 ${titleMarginTop} scroll-mt-24`}
          id="subscription-price"
        >
          {title || t("title")}
        </h2>
        <p className="text-xl text-center text-gray-600 mb-10">
          {description || t("description")}
        </p>

        <Tabs
          value={activePlanType}
          defaultValue={activePlanType}
          onValueChange={(value) => {
            setActivePlanType(value);
            trackEvent("pricing_tab_click", { tab: value });
          }}
          className="w-full mb-8"
        >
          <TabsList className="grid w-full max-w-lg mx-auto grid-cols-3">
            <TabsTrigger value="onetime">
              {t("tabs.onetime")}
              <span className="ml-0.5 inline-block rounded-full bg-custom-bg/10 px-1 py-0 text-[9px] leading-3 text-custom-bg">
                {t("tags.new")}
              </span>
            </TabsTrigger>
            <TabsTrigger value="monthly">{t("tabs.monthly")}</TabsTrigger>
            <TabsTrigger value="yearly">
              {t("tabs.yearly")}
              <span className="ml-0.5 inline-block rounded-full bg-custom-bg/10 px-1 py-0 text-[9px] leading-3 text-custom-bg">
                {t("tabs.savePercent", {
                  discount: plansConfig.basic.yearlyDiscount * 100,
                })}
              </span>
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div
          className={`flex flex-wrap ${
            containerClassName.includes("dialog-pricing") ? "gap-4" : "gap-8"
          } mb-24 px-4 justify-center ${
            containerClassName.includes("dialog-pricing") ? "dialog-cards" : ""
          }`}
        >
          {allTiers
            .filter((tier) =>
              tier.type === "onetime"
                ? activePlanType === "onetime"
                : ["monthly", "yearly"].includes(activePlanType)
            )
            .map((tier) => (
              <Card
                key={tier.canonicalName}
                className={`relative ${
                  containerClassName.includes("dialog-pricing")
                    ? activePlanType === "onetime"
                      ? "w-full sm:w-[calc(33.333%-1rem)] md:max-w-[410px]"
                      : "w-full sm:w-[calc(50%-1rem)] md:max-w-[410px]"
                    : "w-full sm:w-[calc(50%-1rem)] lg:w-[calc(33.333%-1.5rem)]"
                } ${
                  activeTier === tier.canonicalName.toLowerCase()
                    ? "border-myThemeColor"
                    : ""
                } ${
                  tier.canonicalName === "lite" &&
                  FEATURES.PRICE_INCREASE.enabled
                    ? "overflow-hidden"
                    : ""
                }`}
                onMouseEnter={() =>
                  setActiveTier(tier.canonicalName.toLowerCase())
                }
                onMouseLeave={() => setActiveTier(null)}
              >
                {/* 移除了这里的倒计时组件，将在价格部分显示 */}

                {tier.isPopular && (
                  <div className="absolute top-6 right-4">
                    <span className="inline-flex items-center rounded-full bg-custom-bg/10 px-3 mr-2 py-1 text-sm font-medium text-custom-bg">
                      <Flame className="w-4 h-4 mr-1" />
                      {t("tiers.basic.popular")}
                    </span>
                  </div>
                )}
                <CardHeader className="min-h-[140px]">
                  <CardTitle>{tier.name}</CardTitle>
                  <p className="text-foreground-light mb-4 pt-2 pb-4 text-sm 2xl:pr-4">
                    {tier.description}
                  </p>
                </CardHeader>
                <CardContent>
                  {/* 已移除按钮上方的倒计时组件，将显示在价格下方 */}

                  <Button
                    className="w-full mb-6 bg-custom-bg hover:bg-custom-bg-500 focus:bg-custom-bg-600"
                    onClick={() => handleSubscribe(tier)}
                    disabled={loadingTier === tier.canonicalName.toLowerCase()}
                  >
                    {loadingTier === tier.canonicalName.toLowerCase()
                      ? "Loading..."
                      : hasActiveSubscription && tier.type === "subscription"
                        ? t("manageSubscription")
                        : tier.cta}
                  </Button>
                  {tier.type === "onetime" ? (
                    <div className="space-y-2 mb-8 text-center">
                      <div className="flex items-start justify-center pt-4">
                        <p className="font-mono text-5xl">
                          ${tier.discountedPrice}
                        </p>
                        {tier.originalPrice > tier.discountedPrice && (
                          <div className="flex flex-col ml-2 justify-between py-2">
                            <span className="line-through text-gray-400 text-sm">
                              ${tier.originalPrice}
                            </span>
                            <span className="text-custom-bg text-sm">
                              {Math.round(
                                ((tier.originalPrice - tier.discountedPrice) /
                                  tier.originalPrice) *
                                  100
                              )}
                              % OFF
                            </span>
                          </div>
                        )}
                      </div>

                      {tier.canonicalName !== "free" && (
                        <div className="text-sm text-foreground-lighter mt-2">
                          ${(tier.discountedPrice / tier.minutes).toFixed(3)}/
                          {t("billing.minute")}
                        </div>
                      )}

                      <p className="text-foreground-lighter text-[13px] leading-4">
                        {t("billing.oneTimePayment")}
                      </p>

                      {/* 在价格下方显示倒计时 */}
                      {tier.canonicalName === "lite" &&
                        FEATURES.PRICE_INCREASE.enabled &&
                        FEATURES.PRICE_INCREASE.tiers.includes("lite") &&
                        !isPriceIncreaseEnded() && (
                          <div className="mt-3">
                            <PriceIncreaseCountdown
                              targetDate={FEATURES.PRICE_INCREASE.endDate}
                              className="rounded-md shadow-sm"
                            />
                          </div>
                        )}
                    </div>
                  ) : (
                    <div className="mb-8 text-center">
                      <div className="flex items-end justify-center pt-4">
                        <p className="mt-2 pb-1 font-mono text-5xl">
                          {activePlanType === "monthly"
                            ? tier.monthlyPrice
                            : tier.yearlyPrice}
                        </p>
                        <p className="text-foreground-lighter mb-1.5 ml-1 text-[13px] leading-4">
                          {t("billing.perMonth")}
                        </p>
                        {activePlanType === "yearly" &&
                          tier.canonicalName !== "free" && (
                            <div className="flex flex-col ml-2 justify-between py-2">
                              <span className="line-through text-gray-400 text-sm">
                                {tier.monthlyPrice}
                              </span>
                            </div>
                          )}
                      </div>

                      {activePlanType === "yearly" &&
                        tier.canonicalName !== "free" && (
                          <div className="text-sm text-foreground-lighter mt-2">
                            ({tier.yearlyTotalPrice} {t("billing.perYear")})
                          </div>
                        )}

                      <div className="text-sm text-foreground-lighter mt-2">
                        {tier.canonicalName === "free" ? (
                          t("tiers.free.noCreditCard") ||
                          "No credit card required"
                        ) : (
                          <>
                            $
                            {activePlanType === "monthly"
                              ? (
                                  plansConfig[tier.canonicalName.toLowerCase()]
                                    .monthly / tier.minutes
                                ).toFixed(3)
                              : (
                                  (plansConfig[tier.canonicalName.toLowerCase()]
                                    .monthly *
                                    (1 -
                                      plansConfig[
                                        tier.canonicalName.toLowerCase()
                                      ].yearlyDiscount)) /
                                  tier.minutes
                                ).toFixed(3)}
                            /{t("billing.minute")}
                          </>
                        )}
                      </div>
                    </div>
                  )}
                  <ul className="space-y-2">
                    {tier.features.map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="h-5 w-5 text-custom-bg mr-2 mt-1" />
                        <span className="flex flex-wrap items-center gap-1 text-sm">
                          <FeatureItem feature={feature} />
                        </span>
                      </li>
                    ))}
                  </ul>
                  {tier.comingSoonFeatures &&
                    tier.comingSoonFeatures.length > 0 && (
                      <div className="mt-4">
                        <h4 className="text-sm text-gray-400 font-semibold mb-2">
                          {t("features.comingSoon")}
                        </h4>
                        <ul className="space-y-2 text-sm">
                          {tier.comingSoonFeatures.map((feature, index) => (
                            <li key={index} className="flex items-center">
                              <Check className="h-5 w-5 text-gray-400 mr-2" />
                              <span className="text-gray-600 text-sm">
                                {feature}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                </CardContent>
              </Card>
            ))}
        </div>
      </div>
    </div>
  );
};

export default Pricing;
