import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs";
import { toolsService } from "@/services/api/toolsService";
import UploadTab from "./TranscriptionTabs/UploadTab";
import YouTubeTab from "./TranscriptionTabs/YouTubeTab";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { trackEvent, trackAnonymousEvent } from "@/lib/analytics";
import { usePathname } from "next/navigation";
import { useRouter } from "@/i18n/navigation";
import isValidYouTubeUrl from "@/lib/youtube";
import { transcriptionService } from "@/services/api/transcriptionService";
import supabase from "@/lib/supabaseClient";
import { FEATURES } from "@/config/features";
import { storageService } from "@/services/storageService";
import GuestModeDialog from "@/components/Dashboard/GuestModeDialog";
import { useTranscriptionSettings } from "@/hooks/useTranscriptionSettings";

const TranscriptionBox = ({ isLoggedIn }) => {
  const t = useTranslations("transcriptionBox");
  const [url, setUrl] = useState("");
  const [videoData, setVideoData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isTranscribing, setIsTranscribing] = useState(false);
  const [activeTab, setActiveTab] = useState("upload");
  const [shouldShowViewTranscript, setShouldShowViewTranscript] =
    useState(false);
  const [originalUrl, setOriginalUrl] = useState("");
  const [showLimitDialog, setShowLimitDialog] = useState(false);
  const [limitDialogSource, setLimitDialogSource] = useState(
    "youtube_transcription_limit"
  );
  const pathname = usePathname();
  const router = useRouter();

  // 使用统一的转录设置Hook
  const transcriptionSettings = useTranscriptionSettings();

  // 检查localStorage中的YouTube转录状态并恢复
  useEffect(() => {
    const checkYouTubeTranscription = async () => {
      try {
        const anonymousUpload = localStorage.getItem("anonymousUpload");
        if (!anonymousUpload) return;

        const uploadData = JSON.parse(anonymousUpload);

        // 只处理YouTube类型的转录
        if (uploadData.type === "youtube") {
          // 验证转录是否仍然存在
          const { data } = await transcriptionService.getTranscriptionById(
            uploadData.fileId
          );
          if (!data) {
            // 转录不存在，清除localStorage
            localStorage.removeItem("anonymousUpload");
            return;
          }

          // 恢复YouTube转录状态
          setActiveTab("paste");
          const sourceUrl = data.sourceUrl || "";
          setUrl(sourceUrl);
          setShouldShowViewTranscript(true);

          // 保存原始URL，用于后续比较
          setOriginalUrl(sourceUrl);

          // 如果有sourceUrl，尝试获取视频信息
          if (data.sourceUrl) {
            try {
              const videoInfo = await toolsService.getYoutubeInfo(
                data.sourceUrl
              );
              setVideoData(videoInfo);
            } catch (error) {
              console.error("Failed to fetch YouTube video info:", error);
              // 即使获取视频信息失败，也要显示基本信息
              setVideoData({
                title: uploadData.fileName,
                duration: data.duration,
              });
            }
          }
        }
      } catch (error) {
        console.error("Failed to check YouTube transcription:", error);
        // 出错时清除localStorage
        localStorage.removeItem("anonymousUpload");
      }
    };

    checkYouTubeTranscription();
  }, []);

  // 监听URL变化，如果URL发生变化则清除"View transcript"状态
  useEffect(() => {
    if (originalUrl && url !== originalUrl) {
      setShouldShowViewTranscript(false);
    }
  }, [url, originalUrl]);

  const handleSearch = async (searchUrl) => {
    if (!searchUrl) return;

    // 检查YouTube升级状态
    if (FEATURES.YOUTUBE_UPGRADE.enabled) {
      setError(t("youtubeTab.upgradeNotice"));
      setVideoData(null);
      return;
    }

    // 添加 YouTube URL 验证
    const validation = isValidYouTubeUrl(searchUrl);
    if (!validation.isValid) {
      //仅当前端验证searchUrl不满足任何已知YouTube url 模式时，才记录错误
      //其他要么是本来就不是YouTube链接，要么就是后端返回的视频权限之类的问题，没有必要在前端打点上报
      if (validation.reason === "Invalid YouTube video URL") {
        trackEvent("youtube_search_error", {
          url: searchUrl,
          error: "Invalid YouTube video URL",
          source: "youtube_transcription_box",
        });
      }
      setError(validation.reason);
      setVideoData(null);
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      // Use the extracted URL if available
      const urlToUse = validation.extractedUrl || searchUrl;
      const data = await toolsService.getYoutubeInfo(urlToUse);
      setVideoData(data);
    } catch (error) {
      // 检测网络错误
      const isNetworkError = !error.response;

      if (isNetworkError) {
        // 网络错误，显示网络错误信息
        setError(t("youtubeTab.networkError"));
      } else {
        // 其他错误，显示具体错误信息
        setError(error.data?.message);
      }
      setVideoData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUrlChange = async (e) => {
    const newUrl = e.target.value;
    setUrl(newUrl);

    // 清除之前的错误和数据
    setError(null);
    setVideoData(null);

    // // 只有当URL看起来像YouTube URL时才尝试搜索
    // if (
    //   newUrl &&
    //   (newUrl.includes("youtube.com") || newUrl.includes("youtu.be"))
    // ) {
    //   await handleSearch(newUrl);
    // }
  };

  const handleAnonymousTranscribe = async () => {
    if (!url || !videoData) return;

    // 检查匿名用户YouTube转录次数限制
    if (!storageService.canTranscribeYoutube()) {
      setShowLimitDialog(true);
      setLimitDialogSource("youtube_transcription_limit");
      return;
    }

    // 检查匿名用户30分钟限制
    const durationInMinutes = Math.ceil(videoData.duration / 60); // 将秒转换为分钟，向上取整

    // 检查是否已经超过30分钟限制
    if (storageService.hasExceededMinutesLimit()) {
      setShowLimitDialog(true);
      setLimitDialogSource("minutes_limited");
      return;
    }

    // 检查新视频时长加上已有时长是否会超过30分钟
    if (!storageService.canTranscribeWithDuration(durationInMinutes)) {
      setShowLimitDialog(true);
      setLimitDialogSource("minutes_limited");
      return;
    }

    setIsTranscribing(true);
    setError(null);

    try {
      // 匿名用户登录逻辑
      const {
        data: { session },
      } = await supabase.auth.getSession();
      if (!session) {
        const { error } = await supabase.auth.signInAnonymously();
        if (error) throw error;
      }

      trackAnonymousEvent("youtube_transcribe_start", {
        path: pathname,
      });

      if (!videoData.duration || isNaN(videoData.duration)) {
        setError("Invalid video duration");
        trackAnonymousEvent("youtube_transcribe_error", {
          error: "Invalid video duration",
        });
        setIsTranscribing(false);
        return;
      }

      // 检查URL并提取
      const validation = isValidYouTubeUrl(url);
      const urlToUse = validation.isValid
        ? validation.extractedUrl || url
        : url;

      // 创建YouTube转录任务
      const response =
        await transcriptionService.createYoutubeTranscriptionTask(
          urlToUse,
          videoData.title,
          videoData.duration,
          transcriptionSettings.subtitleEnabled,
          transcriptionSettings.selectedLanguage,
          transcriptionSettings.enableSpeakerDiarization,
          null // 匿名用户不设置文件夹
        );

      if (response.status === 200) {
        trackAnonymousEvent("youtube_transcribe_success", {
          language: transcriptionSettings.selectedLanguage,
          subtitleEnabled: transcriptionSettings.subtitleEnabled,
          enableSpeakerDiarization:
            transcriptionSettings.enableSpeakerDiarization,
        });

        // 增加YouTube转录次数计数和分钟数
        const durationInMinutes = Math.ceil(videoData.duration / 60);
        storageService.incrementAnonymousYoutubeCount(durationInMinutes);

        // 存储匿名转录信息
        localStorage.setItem(
          "anonymousUpload",
          JSON.stringify({
            fileId: response.data.id,
            fileName: videoData.title,
            timestamp: new Date().toISOString(),
            fromPage: pathname,
            type: "youtube",
          })
        );

        // 跳转到转录详情页 - 保持按钮禁用状态直到页面跳转
        router.push(`/transcriptions/${response.data.id}`);
        // 成功时不重置isTranscribing，让按钮保持禁用状态直到页面跳转
      }
    } catch (error) {
      let errorMsg = "An error occurred";
      if (error.response?.data?.message) {
        errorMsg = error.response.data.message;
      } else if (error.message) {
        errorMsg = error.message;
      }

      setError(errorMsg);
      trackAnonymousEvent("youtube_transcribe_error", {
        error: errorMsg,
        status: error.response?.status,
      });
      // 只在错误情况下重置状态
      setIsTranscribing(false);
    }
  };

  const handleTranscribeClick = async (e) => {
    e.preventDefault();

    trackEvent("youtube_transcribe_button_click", {
      isLoggedIn: !!isLoggedIn,
      path: pathname,
    });

    // 如果已登录，跳转到dashboard
    if (isLoggedIn) {
      router.push("/dashboard");
      return;
    }

    // 如果是"View transcript"状态，直接跳转到转录详情页
    if (shouldShowViewTranscript) {
      const anonymousUpload = localStorage.getItem("anonymousUpload");
      if (anonymousUpload) {
        const uploadData = JSON.parse(anonymousUpload);
        router.push(`/transcriptions/${uploadData.fileId}`);
        return;
      }
    }

    // 如果未登录且有视频数据，开始匿名转录
    if (videoData && url) {
      await handleAnonymousTranscribe();
    } else {
      // 如果没有视频数据，跳转到登录页
      router.push("/auth/signin");
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto bg-white rounded-xl md:p-6">
      <Tabs
        value={activeTab}
        className="w-full md:w-4/5 mx-auto"
        onValueChange={(value) => {
          setActiveTab(value);
          // 移除了自动填充默认URL的逻辑，现在切换到YouTube标签页时输入框为空

          // 切换标签页时重置说话人识别状态和高级设置展开状态
          transcriptionSettings.updateSettings({
            enableSpeakerDiarization: false,
            advancedSettingsOpen: false,
          });
        }}
      >
        <TabsList className="grid w-full grid-cols-2 h-12 bg-white">
          <TabsTrigger
            value="upload"
            className="relative text-base md:text-lg font-medium px-0 data-[state=active]:font-bold after:absolute after:left-[25%] after:right-[25%] after:bottom-0 after:h-[3px] after:bg-transparent data-[state=active]:after:bg-custom-bg"
          >
            {t("tabs.upload")}
          </TabsTrigger>
          <TabsTrigger
            value="paste"
            className="relative text-base md:text-lg font-medium px-0 data-[state=active]:font-bold after:absolute after:left-[25%] after:right-[25%] after:bottom-0 after:h-[3px] after:bg-transparent data-[state=active]:after:bg-custom-bg"
          >
            {t("tabs.paste")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload">
          <UploadTab />
        </TabsContent>

        <TabsContent value="paste" className="flex flex-col">
          <div className="flex-1 mb-6">
            <YouTubeTab
              url={url}
              onUrlChange={handleUrlChange}
              onSearch={handleSearch}
              isLoading={isLoading}
              videoData={videoData}
              error={error}
              transcriptionSettings={transcriptionSettings}
            />
          </div>
          {/* 只有在用户未登录时才显示转录按钮 */}
          {!isLoggedIn && (
            <div className="w-full max-w-xs md:max-w-lg mx-auto mb-6 md:mb-0">
              <Button
                className="w-full h-10 px-6 text-white bg-custom-bg hover:bg-custom-bg/90 transition-colors"
                onClick={handleTranscribeClick}
                disabled={isTranscribing || isLoading}
              >
                {isTranscribing
                  ? t("youtubeTab.processing")
                  : shouldShowViewTranscript
                  ? t("youtubeTab.viewTranscript")
                  : t("transcribeButton")}
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>

      {/* 转录次数限制弹框 */}
      <GuestModeDialog
        isOpen={showLimitDialog}
        onClose={() => setShowLimitDialog(false)}
        source={limitDialogSource}
        remainingMinutes={storageService.getAnonymousRemainingMinutes()}
        requiredMinutes={videoData ? Math.ceil(videoData.duration / 60) : 0}
      />
    </div>
  );
};

export default TranscriptionBox;
