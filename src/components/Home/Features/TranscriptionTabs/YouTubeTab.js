import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { formatDuration } from "@/lib/utils";
import { Clock } from "lucide-react";
import { useTranslations } from "next-intl";
import { useRouter } from "@/i18n/navigation";
import { useAuthStore } from "@/stores/useAuthStore";
import TranscriptionSettings from "@/components/Common/TranscriptionSettings/TranscriptionSettings";
import { FEATURES } from "@/config/features";

const YouTubeTab = ({
  url,
  onUrlChange,
  onSearch,
  isLoading,
  videoData,
  error,
  // 转录设置相关props
  transcriptionSettings,
}) => {
  const t = useTranslations("transcriptionBox.youtubeTab");
  const tCommon = useTranslations("common.uploadPrompt");
  const router = useRouter();
  const { user } = useAuthStore();

  // 检查用户是否已登录（排除匿名用户）
  const isLoggedIn = user && !user.isAnonymous;

  const handleGotoDashboard = () => {
    router.push("/dashboard");
  };

  // 检查YouTube升级状态
  const isYouTubeUpgrading = FEATURES.YOUTUBE_UPGRADE.enabled;

  // 处理搜索时的升级检查
  const handleSearchWithUpgradeCheck = (url) => {
    if (isYouTubeUpgrading) {
      // 不执行搜索，直接返回
      return;
    }
    onSearch(url);
  };

  // 如果用户已登录，显示"Go to dashboard"部分
  if (isLoggedIn) {
    return (
      <>
        <p className="my-4 text-sm md:text-base text-center text-muted-foreground">
          {t("description")}
        </p>
        <div className="border-2 border-dashed border-custom-bg/60 rounded-lg p-8 transition-colors duration-200 hover:border-custom-bg">
          <div className="flex flex-col items-center space-y-4">
            <Button
              className="md:mt-2 bg-custom-bg hover:bg-custom-bg-600"
              onClick={handleGotoDashboard}
            >
              {tCommon("loggedIn.gotoDashboard")}
            </Button>

            <p className="text-sm md:text-base text-center text-muted-foreground">
              {tCommon("loggedIn.description")}
            </p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <p className="my-4 text-sm md:text-base text-center text-muted-foreground">
        {t("description")}
      </p>
      <div className="space-y-4">
        <div className="flex gap-2 w-full max-w-xs md:max-w-lg mx-auto">
          <Input
            type="url"
            placeholder={t("placeholder")}
            value={url}
            onChange={onUrlChange}
            className="flex-1 p-3 placeholder:text-gray-300 focus:outline-none focus-visible:border-custom-bg focus-visible:ring-0"
          />
          <Button
            onClick={() => handleSearchWithUpgradeCheck(url)}
            disabled={!url}
            className="bg-custom-bg hover:bg-custom-bg/90 disabled:opacity-50 whitespace-nowrap"
          >
            {isLoading ? t("searching") : t("search")}
          </Button>
        </div>

        {/* 显示升级提示或错误信息 */}
        {isYouTubeUpgrading ? (
          <div className="mt-2 text-center">
            <p className="text-orange-600 text-sm font-medium" role="alert">
              {t("upgradeNotice")}
            </p>
          </div>
        ) : error ? (
          <div className="mt-2 text-center">
            <p className="text-destructive text-sm font-medium" role="alert">
              {error}
            </p>
          </div>
        ) : null}

        {videoData && (
          <div className="mt-4">
            <div className="flex flex-col gap-4">
              <div className="w-full text-center">
                <h3 className="font-medium text-sm md:text-base line-clamp-2 break-words">
                  {videoData.title}
                </h3>

                <div className="flex items-center justify-center mt-2">
                  <p className="text-xs md:text-sm text-gray-500 flex items-center">
                    <Clock className="h-4 w-4 mr-1" />
                    {formatDuration(videoData.duration)}
                  </p>
                </div>
              </div>

              <div className="w-full max-w-xs mx-auto h-auto aspect-video relative">
                <img
                  src={videoData.thumbnailUrl}
                  alt={videoData.title}
                  className="w-full h-full object-cover rounded"
                />
              </div>

              {/* 使用统一的转录设置组件 */}
              <div className="w-full max-w-xs mx-auto">
                <TranscriptionSettings
                  selectedLanguage={transcriptionSettings.selectedLanguage}
                  subtitleEnabled={transcriptionSettings.subtitleEnabled}
                  enableSpeakerDiarization={
                    transcriptionSettings.enableSpeakerDiarization
                  }
                  advancedSettingsOpen={
                    transcriptionSettings.advancedSettingsOpen
                  }
                  showPremiumDialog={transcriptionSettings.showPremiumDialog}
                  onLanguageSelect={transcriptionSettings.handleLanguageSelect}
                  onSubtitleChange={transcriptionSettings.handleSubtitleChange}
                  onSpeakerDiarizationChange={
                    transcriptionSettings.handleSpeakerDiarizationChange
                  }
                  onAdvancedSettingsToggle={
                    transcriptionSettings.handleAdvancedSettingsToggle
                  }
                  onPremiumDialogClose={() =>
                    transcriptionSettings.setShowPremiumDialog(false)
                  }
                  containerClassName="space-y-4"
                  premiumDialogSource="youtube_tab_speaker_recognition"
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default YouTubeTab;
