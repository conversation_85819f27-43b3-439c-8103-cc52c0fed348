import YoutubeTranscriptExtractor from "@/components/Tools/YoutubeTranscriptExtractor";
import FeatureUnavailable from "@/components/Common/FeatureUnavailable";
import { Suspense } from "react";
import { renderSchemas } from "@/lib/schema";
import { getTranslations } from "next-intl/server";
import { useTranslations } from "next-intl";
import {
  generateConsistentMetadata,
  generateStructuredData,
} from "@/lib/metadata";

export async function generateMetadata({ params: { locale }, searchParams }) {
  const t = await getTranslations({
    locale,
    namespace: "tools.youtubeTranscript.meta",
  });

  // 直接返回 noindex 元数据，因为功能已下线
  return {
    title: t("title"),
    description: t("description"),
    robots: {
      index: false,
      follow: false,
      noarchive: true,
      nosnippet: true,
      noimageindex: true,
      googleBot: {
        index: false,
        follow: false,
        noarchive: true,
        nosnippet: true,
        noimageindex: true,
      },
    },
  };

  // 以下代码保留但不执行
  return generateConsistentMetadata(
    "/tools/youtube-transcript-extractor",
    locale,
    {
      title: t("title"),
      description: t("description"),
      ogTitle: t("ogTitle"),
      ogDescription: t("ogDescription"),
      ogImage: "/og/youtube-transcript-extractor.png",
      twitterTitle: t("twitterTitle"),
      twitterDescription: t("twitterDescription"),
      twitterImage: "/og/youtube-transcript-extractor.png",
    }
  );
}

export default function YoutubeTranscriptExtractorPage({
  params,
  searchParams,
}) {
  const t = useTranslations("tools.youtubeTranscript");

  // 直接显示功能不可用页面
  return <FeatureUnavailable />;

  // 以下代码保留但不执行
  // 使用新的 generateStructuredData 函数生成结构化数据
  const structuredData = generateStructuredData(
    "/tools/youtube-transcript-extractor",
    params.locale,
    {
      title: t("meta.title"),
      description: t("meta.description"),
    }
  );

  return (
    <>
      <Suspense fallback={<div>{t("loading")}</div>}>
        {renderSchemas(structuredData)}
        <YoutubeTranscriptExtractor />
      </Suspense>
    </>
  );
}
