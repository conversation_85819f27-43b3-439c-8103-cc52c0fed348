import YoutubeDownloader from "@/components/Tools/YoutubeDownloader";
import FeatureUnavailable from "@/components/Common/FeatureUnavailable";
import { Suspense } from "react";
import { renderSchemas } from "@/lib/schema";
import RecommendedTools from "@/components/Common/RecommendedTools";
import FAQ from "@/components/Common/FAQ";
import { getTranslations } from "next-intl/server";
import {
  generateConsistentMetadata,
  generateStructuredData,
} from "@/lib/metadata";

export async function generateMetadata({ params: { locale }, searchParams }) {
  const t = await getTranslations("tools.youtubeDownloader.meta");

  // 如果没有 URL 参数，返回 noindex 元数据
  if (!searchParams?.url) {
    return {
      title: t("title"),
      description: t("description"),
      robots: {
        index: false,
        follow: false,
        noarchive: true,
        nosnippet: true,
        noimageindex: true,
        googleBot: {
          index: false,
          follow: false,
          noarchive: true,
          nosnippet: true,
          noimageindex: true,
        },
      },
    };
  }

  return generateConsistentMetadata("/tools/youtube-video-downloader", locale, {
    title: t("title"),
    description: t("description"),
    ogTitle: t("title"),
    ogDescription: t("description"),
    ogImage: "/og/youtube-downloader.png",
    twitterTitle: t("title"),
    twitterDescription: t("description"),
    twitterImage: "/og/youtube-downloader.png",
  });
}

export default async function YoutubeDownloaderPage({ params, searchParams }) {
  const t = await getTranslations("tools.youtubeDownloader");

  // 检查是否有 URL 参数，如果有则说明是从 YouTubeUploadContent 跳转过来的
  const hasUrlParam = searchParams?.url;

  // 如果没有 URL 参数，显示功能不可用页面
  if (!hasUrlParam) {
    return <FeatureUnavailable />;
  }

  // 使用新的 generateStructuredData 函数生成结构化数据
  const structuredData = generateStructuredData(
    "/tools/youtube-video-downloader",
    params.locale,
    {
      title: t("meta.title"),
      description: t("meta.description"),
    }
  );

  // 创建 FAQ 数组
  const faqs = [
    {
      question: t("faq.what.question"),
      answer: t("faq.what.answer"),
    },
    {
      question: t("faq.legal.question"),
      answer: t("faq.legal.answer"),
    },
    {
      question: t("faq.quality.question"),
      answer: t("faq.quality.answer"),
    },
    {
      question: t("faq.audio.question"),
      answer: t("faq.audio.answer"),
    },
    {
      question: t("faq.limit.question"),
      answer: t("faq.limit.answer"),
    },
    {
      question: t("faq.account.question"),
      answer: t("faq.account.answer"),
    },
    {
      question: t("faq.browsers.question"),
      answer: t("faq.browsers.answer"),
    },
    {
      question: t("faq.unavailable.question"),
      answer: t("faq.unavailable.answer"),
    },
  ];

  return (
    <>
      <Suspense fallback={<div>{t("loading")}</div>}>
        {renderSchemas(structuredData)}
        <YoutubeDownloader />

        <div className="max-w-3xl mx-auto px-4 pt-12 sm:px-6">
          <RecommendedTools />
          <FAQ faqs={faqs} />
        </div>
      </Suspense>
    </>
  );
}
