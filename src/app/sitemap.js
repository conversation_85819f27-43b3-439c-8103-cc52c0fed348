import { KEYWORD_PAGES as GENERAL_KEYWORD_PAGES } from "@/messages/en/keywords";
import { KEYWORD_PAGES as LANGUAGE_KEYWORD_PAGES } from "@/messages/en/language-keywords";
import { defaultLocale, locales } from "@/config/i18n";
import fs from "fs/promises";
import path from "path";

export default async function sitemap() {
  const baseUrl = "https://www.uniscribe.co";

  // 生成URL的辅助函数，处理默认语言不带前缀的情况
  const getLocalizedUrl = (locale, path) => {
    if (locale === defaultLocale) {
      return `${baseUrl}${path}`;
    }
    return `${baseUrl}/${locale}${path}`;
  };

  // 为每个语言生成常规关键词页面路由
  const generalKeywordRoutes = locales.flatMap((locale) =>
    Object.entries(GENERAL_KEYWORD_PAGES)
      .filter(([_, pageData]) => !pageData.redirect)
      .map(([keyword]) => ({
        url: getLocalizedUrl(locale, `/l/${keyword}`),
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 0.9,
      }))
  );

  // 为每个语言生成语言相关页面路由
  const languageRoutes = locales.flatMap((locale) => [
    // 语言主页
    {
      url: getLocalizedUrl(locale, "/languages"),
      lastModified: new Date(),
      changeFrequency: "weekly",
      priority: 0.8,
    },
    // 语言子页面
    ...Object.entries(LANGUAGE_KEYWORD_PAGES)
      .filter(([_, pageData]) => !pageData.redirect)
      .map(([keyword]) => ({
        url: getLocalizedUrl(locale, `/languages/${keyword}`),
        lastModified: new Date(),
        changeFrequency: "daily",
        priority: 0.8,
      })),
  ]);

  // 获取每种语言的博客文件
  const blogRoutes = await Promise.all(
    locales.map(async (locale) => {
      // 使用正确的语言特定目录
      const localeDir = locale === "en" ? "en" : locale;
      const localeBlogsDirectory = path.join(
        process.cwd(),
        `contents/blogs/${localeDir}`
      );

      try {
        // 检查语言特定目录是否存在
        await fs.access(localeBlogsDirectory);

        // 读取该语言的博客文件
        const blogFiles = await fs.readdir(localeBlogsDirectory);

        // 为每个博客文件创建正确的本地化URL
        return blogFiles
          .filter((fileName) => fileName.endsWith(".md")) // 确保只处理markdown文件
          .map((fileName) => ({
            url: getLocalizedUrl(
              locale,
              `/blog/${fileName.replace(".md", "")}`
            ),
            lastModified: new Date(),
            changeFrequency: "weekly",
            priority: 0.8,
          }));
      } catch (error) {
        // 如果该语言目录不存在，检查是否可以使用英文作为回退
        if (locale !== "en") {
          try {
            const enBlogsDirectory = path.join(
              process.cwd(),
              `contents/blogs/en`
            );
            await fs.access(enBlogsDirectory);

            const enBlogFiles = await fs.readdir(enBlogsDirectory);
            return enBlogFiles
              .filter((fileName) => fileName.endsWith(".md"))
              .map((fileName) => ({
                url: getLocalizedUrl(
                  locale,
                  `/blog/${fileName.replace(".md", "")}`
                ),
                lastModified: new Date(),
                changeFrequency: "weekly",
                priority: 0.8,
              }));
          } catch (fallbackError) {
            console.warn(`No fallback English blog directory found`);
            return [];
          }
        }

        console.warn(`No blog directory found for locale: ${locale}`);
        return [];
      }
    })
  ).then((results) => results.flat());

  // 主要路由路径列表（添加优先级配置）
  const mainPaths = [
    { url: "", priority: 1.0 }, // 首页最高优先级
    { url: "/pricing", priority: 0.9 },
    { url: "/features", priority: 0.9 },
    { url: "/blog", priority: 0.8 },
    { url: "/tools/video-to-audio-extractor", priority: 0.7 },
    { url: "/tools/wav-to-mp3-converter", priority: 0.7 },
    { url: "/terms-of-service", priority: 0.3 },
    { url: "/privacy-policy", priority: 0.3 },
    { url: "/auth/signin", priority: 0.1 },
    { url: "/auth/signup", priority: 0.1 },
  ];

  const excludedPaths = ["/auth"];

  // 检查路径是否在排除列表中
  const isExcludedPath = (path) => {
    return excludedPaths.some((excludedPath) => path.startsWith(excludedPath));
  };

  // 生成所有路径的 sitemap 条目
  const routes = mainPaths.flatMap((pathConfig) => {
    const path = typeof pathConfig === "string" ? pathConfig : pathConfig.url;
    const priority = typeof pathConfig === "object" ? pathConfig.priority : 0.5; // 默认值

    if (isExcludedPath(path)) {
      return [
        {
          url: `${baseUrl}${path}`,
          lastModified: new Date(),
          priority: 0.1, // 被排除路径低优先级
        },
      ];
    }

    return locales.map((lang) => ({
      url: getLocalizedUrl(lang, path),
      lastModified: new Date(),
      priority, // 使用配置的优先级
      changeFrequency: "weekly",
    }));
  });

  // 合并所有路由
  return [...routes, ...generalKeywordRoutes, ...languageRoutes, ...blogRoutes];
}
