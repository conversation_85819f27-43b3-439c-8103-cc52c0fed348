import fs from "fs/promises";
import path from "path";
import { AzureOpenAI } from "openai";
import dotenv from "dotenv";

// 加载环境变量
dotenv.config();

// Azure OpenAI 配置
const deployment = process.env.AZURE_DEPLOYMENT_NAME || "gpt-4";
const apiVersion = "2024-10-21";

// 检查必要的环境变量
if (!process.env.AZURE_OPENAI_ENDPOINT) {
  throw new Error("Missing AZURE_OPENAI_ENDPOINT environment variable");
}

// 创建 client
const client = new AzureOpenAI({
  deployment,
  apiVersion,
  endpoint: process.env.AZURE_OPENAI_ENDPOINT,
  apiKey: process.env.AZURE_OPENAI_API_KEY, // 如果使用 API key 认证
});

// 从 i18n.js 复制需要的配置
const locales = [
  "en", // English
  "zh", // Chinese (Simplified)
  "zh-TW", // Chinese (Traditional)
  "es", // Spanish
  "ar", // Arabic
  "fr", // French
  "ru", // Russian
  "pt", // Portuguese
  "id", // Indonesian
  "fa", // Persian
  "de", // German
  "ja", // Japanese
  "tr", // Turkish
  "it", // Italian
  "vi", // Vietnamese
  "ko", // Korean
  "pl", // Polish
  "nl", // Dutch
  "uk", // Ukrainian
  "he", // Hebrew
  "cs", // Czech
  "da", // Danish
  "fi", // Finnish
  "el", // Greek
  "ms", // Malay
  "ro", // Romanian
  "sv", // Swedish
  "th", // Thai
  "hu", // Hungarian
  "sr", // Serbian
  "nb", // Norwegian Bokmål
  "ca", // Catalan
];

// 添加不需要翻译的术语列表
const doNotTranslateTerms = [
  "Lite",
  "Plus",
  "Pro",
  "Basic",
  "Free",
  "Max",
  // 可以根据需要添加更多术语
];

// 处理语言参数
function processLanguages(langArg) {
  if (langArg === "all") {
    // 过滤掉 'en'，因为它是源语言
    return locales.filter((lang) => lang !== "en");
  }
  return langArg.split(",");
}

// 主命令处理函数
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const skipExisting = args.includes("--skip-existing");

  switch (command) {
    case "init":
      if (args.length < 2) {
        console.error("Error: init command requires language codes");
        console.log(`
Usage for init:
  node translate-messages.mjs init <lang1,lang2,...> [--skip-existing]
  node translate-messages.mjs init all [--skip-existing]
  Example: node translate-messages.mjs init zh
  Example: node translate-messages.mjs init all
        `);
        process.exit(1);
      }
      const targetLangs = processLanguages(args[1]);
      await initializeTranslations(targetLangs, { skipExisting });
      break;

    case "update":
      if (args.length < 3) {
        console.error(
          "Error: update command requires a file path and language codes"
        );
        console.log(`
Usage for update:
  node translate-messages.mjs update <file-path> <lang1,lang2,...> [--skip-existing]
  node translate-messages.mjs update <file-path> all [--skip-existing]
  Example: node translate-messages.mjs update src/messages/en/home.json zh
  Example: node translate-messages.mjs update src/messages/en/home.json all
        `);
        process.exit(1);
      }
      const filePath = args[1];
      const updateLangs = processLanguages(args[2]);
      await updateTranslation(filePath, updateLangs, { skipExisting });
      break;

    case "update-keys":
      if (args.length < 4) {
        console.error(
          "Error: update-keys command requires a file path, keys, and language codes"
        );
        console.log(`
Usage for update-keys:
  node translate-messages.mjs update-keys <file-path> <key1,key2,...> <lang1,lang2,...> [--skip-existing]
  node translate-messages.mjs update-keys <file-path> <key1,key2,...> all [--skip-existing]
  Example: node translate-messages.mjs update-keys src/messages/en/home.json "hero.title,hero.description" zh
  Example: node translate-messages.mjs update-keys src/messages/en/home.json "pricing.title" all
        `);
        process.exit(1);
      }
      const keyFilePath = args[1];
      const keys = args[2].split(",");
      const keyUpdateLangs = processLanguages(args[3]);
      await updateTranslationKeys(keyFilePath, keys, keyUpdateLangs, {
        skipExisting,
      });
      break;

    default:
      console.log(`
Usage:
  Initialize translations for all files:
    node translate-messages.mjs init <lang1,lang2,...>
    Example: node translate-messages.mjs init zh,ja,ko

  Update single file:
    node translate-messages.mjs update <file-path> <lang1,lang2,...>
    Example: node translate-messages.mjs update src/messages/en/home.json zh,ja
    
  Update specific keys in a file:
    node translate-messages.mjs update-keys <file-path> <key1,key2,...> <lang1,lang2,...>
    Example: node translate-messages.mjs update-keys src/messages/en/home.json "hero.title,hero.description" zh,ja
      `);
      process.exit(1);
  }
}

// 初始化翻译
async function initializeTranslations(targetLangs, options = {}) {
  try {
    const sourceDir = path.join(process.cwd(), "src/messages/en");

    for (const lang of targetLangs) {
      console.log(`\nInitializing translations for ${lang}...`);
      const targetDir = path.join(process.cwd(), `src/messages/${lang}`);

      // 创建目标目录
      await fs.mkdir(targetDir, { recursive: true });

      // 获取所有需要翻译的文件
      const files = await getAllFiles(sourceDir);
      console.log(`Found ${files.length} files to translate`);

      // 翻译每个文件
      for (const file of files) {
        const relativePath = path.relative(sourceDir, file);
        const targetPath = path.join(targetDir, relativePath);

        // 确保目标目录存在
        await fs.mkdir(path.dirname(targetPath), { recursive: true });

        console.log(`\nTranslating ${relativePath} to ${lang}`);
        await translateFile(file, targetPath, lang, options);
      }

      // 复制所有 index.js 文件
      await copyIndexFiles(sourceDir, targetDir);
    }

    console.log("\nInitialization completed!");
  } catch (error) {
    console.error("Initialization failed:", error);
    throw error;
  }
}

// 新增：复制 index.js 文件的函数
async function copyIndexFiles(sourceDir, targetDir) {
  try {
    const indexFiles = await findAllIndexFiles(sourceDir);

    for (const indexFile of indexFiles) {
      const relativePath = path.relative(sourceDir, indexFile);
      const targetPath = path.join(targetDir, relativePath);

      // 确保目标目录存在
      await fs.mkdir(path.dirname(targetPath), { recursive: true });

      // 复制文件
      await fs.copyFile(indexFile, targetPath);
      console.log(`📄 Copied index file: ${relativePath}`);
    }
  } catch (error) {
    console.error("Error copying index files:", error);
    throw error;
  }
}

// 新增：查找所有 index.js 文件
async function findAllIndexFiles(dir) {
  const results = [];
  const entries = await fs.readdir(dir, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);

    if (entry.isDirectory()) {
      if (entry.name !== "node_modules") {
        results.push(...(await findAllIndexFiles(fullPath)));
      }
    } else if (entry.name === "index.js") {
      results.push(fullPath);
    }
  }

  return results;
}

// 修改 updateTranslation 函数，添加 index.js 复制功能
async function updateTranslation(filePath, targetLangs, options = {}) {
  try {
    const sourcePath = path.resolve(process.cwd(), filePath);
    const sourceDir = path.join(process.cwd(), "src/messages/en");
    const relativePath = path.relative(sourceDir, sourcePath);
    const dirPath = path.dirname(relativePath);

    for (const lang of targetLangs) {
      console.log(`\nUpdating translation for ${lang}...`);
      const targetDir = path.join(process.cwd(), "src/messages", lang);
      const targetPath = path.join(targetDir, relativePath);

      // 确保目标目录存在
      await fs.mkdir(path.dirname(targetPath), { recursive: true });

      console.log(`Translating ${path.basename(sourcePath)} to ${lang}`);
      await translateFile(sourcePath, targetPath, lang, options);

      // 如果更新的不是 index.js，则复制相关目录的 index.js
      if (path.basename(sourcePath) !== "index.js") {
        const sourceIndexPath = path.join(sourceDir, dirPath, "index.js");
        const targetIndexPath = path.join(targetDir, dirPath, "index.js");

        try {
          await fs.access(sourceIndexPath);
          await fs.copyFile(sourceIndexPath, targetIndexPath);
          console.log(`📄 Updated index.js in ${dirPath}`);
        } catch (error) {
          // 如果源 index.js 不存在，则跳过
          console.log(`No index.js found in ${dirPath}, skipping...`);
        }
      }
    }

    console.log("\nUpdate completed!");
  } catch (error) {
    console.error("Update failed:", error);
    throw error;
  }
}

// 获取所有需要翻译的文件
async function getAllFiles(dir) {
  const files = [];
  const entries = await fs.readdir(dir, { withFileTypes: true });

  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory()) {
      if (entry.name !== "node_modules") {
        files.push(...(await getAllFiles(fullPath)));
      }
    } else {
      // 排除 index.js 和其他不需要翻译的文件
      if (!entry.name.startsWith("index.") && !entry.name.startsWith(".")) {
        files.push(fullPath);
      }
    }
  }

  // 对文件进行排序，.json 文件优先于 .js 文件
  return files.sort((a, b) => {
    const extA = path.extname(a);
    const extB = path.extname(b);

    if (extA === ".json" && extB === ".js") return -1;
    if (extA === ".js" && extB === ".json") return 1;
    return a.localeCompare(b); // 同类型文件按名称排序
  });
}

// 翻译单个文件
async function translateFile(sourcePath, targetPath, targetLang, options = {}) {
  try {
    // 检查目标文件是否已存在
    try {
      const targetStat = await fs.stat(targetPath);
      if (targetStat.isFile()) {
        if (options.skipExisting) {
          console.log(
            `⏭️  Skipping existing file: ${path.basename(targetPath)}`
          );
          return;
        } else {
          console.log(
            `🔄 Overwriting existing file: ${path.basename(targetPath)}`
          );
        }
      }
    } catch (err) {
      // 文件不存在，继续翻译
    }

    const content = await fs.readFile(sourcePath, "utf-8");
    const ext = path.extname(sourcePath);

    let parsedContent;
    if (ext === ".js") {
      // 处理 JavaScript 文件（如 keywords.js）
      parsedContent = parseKeywordsFile(content);
      const translatedChunks = await translateInChunks(
        parsedContent,
        targetLang
      );

      // 验证翻译结果
      const isValid = await validateTranslation(content, translatedChunks);
      if (!isValid) {
        console.warn(
          `Warning: Translation validation failed for ${sourcePath}`
        );
      }

      const outputContent = await reconstructOutput(translatedChunks);
      await fs.writeFile(targetPath, outputContent, "utf-8");
    } else {
      // 处理 JSON 文件
      parsedContent = JSON.parse(content);
      const translatedContent = await translateJson(parsedContent, targetLang);
      await fs.writeFile(
        targetPath,
        JSON.stringify(translatedContent, null, 2),
        "utf-8"
      );
    }

    console.log(`Successfully translated ${path.basename(sourcePath)}`);
  } catch (error) {
    console.error(`Error translating ${sourcePath}:`, error);
    throw error;
  }
}

// 翻译 JSON 内容
async function translateJson(json, targetLang) {
  const translatedJson = {};

  for (const [key, value] of Object.entries(json)) {
    if (typeof value === "string") {
      // 翻译字符串值
      translatedJson[key] = await translateText(value, targetLang);
    } else if (Array.isArray(value)) {
      // 翻译数组
      translatedJson[key] = await Promise.all(
        value.map((item) =>
          typeof item === "string"
            ? translateText(item, targetLang)
            : translateJson(item, targetLang)
        )
      );
    } else if (typeof value === "object" && value !== null) {
      // 递归翻译嵌套对象
      translatedJson[key] = await translateJson(value, targetLang);
    } else {
      // 保持其他类型的值不变
      translatedJson[key] = value;
    }
  }

  return translatedJson;
}

// 修改翻译单个文本的函数
async function translateText(text, targetLang) {
  if (!text || typeof text !== "string") {
    return text;
  }

  try {
    const response = await client.chat.completions.create({
      model: deployment,
      messages: [
        {
          role: "system",
          content: `You are a professional technical translator specializing in software localization.
Task: Translate the following text from English to ${targetLang}.
Context: This is for a professional audio/video transcription software interface.

Requirements:
1. Maintain professional and technical tone
2. Focus on accuracy and clarity
3. Avoid colloquial or casual expressions
4. Keep all technical terms unchanged
5. DO NOT translate template variables like {price}, {count}, {name}, etc. - keep them exactly as they are
6. DO NOT translate the following terms, keep them exactly as they are: ${doNotTranslateTerms.join(", ")}
7. Return only the direct translation`,
        },
        {
          role: "user",
          content: text,
        },
      ],
      temperature: 0.1,
      max_tokens: 1000,
    });

    if (!response?.choices?.[0]?.message?.content) {
      console.log(
        "Content filter results:",
        response?.choices?.[0].content_filter_results
      );
      console.warn(`Warning: Empty translation response for text: "${text}"`);
      return text;
    }

    return response.choices[0].message.content.trim();
  } catch (error) {
    console.warn(`Warning: Translation API error:`, error.message);
    return text;
  }
}

// 修改解析函数，更精确地处理 JSON 结构
function parseKeywordsFile(content) {
  // 移除 export const 声明，只保留对象内容
  const jsonContent = content
    .replace(/export\s+const\s+KEYWORD_PAGES\s*=\s*/, "")
    .replace(/;?\s*$/, "");

  try {
    // 解析为 JavaScript 对象
    const keywordsObj = eval("(" + jsonContent + ")");

    // 将对象转换为可翻译的块
    const chunks = [];
    for (const [key, value] of Object.entries(keywordsObj)) {
      chunks.push({
        key,
        value: JSON.stringify(value, null, 2),
      });
    }
    return chunks;
  } catch (error) {
    console.error("Error parsing keywords file:", error);
    throw error;
  }
}

// 分块翻译
async function translateInChunks(chunks, targetLang) {
  const translatedChunks = [];

  for (const chunk of chunks) {
    const translatedChunk = await translateChunk(chunk, targetLang);
    console.log("translated chunk", translatedChunk);
    translatedChunks.push(translatedChunk);

    // 添加延迟以避免 API 限制
    // await delay(100);
  }

  return translatedChunks;
}

// 添加错误收集器
function createErrorCollector() {
  const errors = new Map();

  return {
    addError: (key, original, translation, error) => {
      errors.set(key, {
        original,
        translation,
        error: error.message || String(error),
        timestamp: new Date().toISOString(),
      });
    },
    hasErrors: () => errors.size > 0,
    getErrors: () => Object.fromEntries(errors),
  };
}

// 修改翻译函数，保存原始值
async function translateChunk(chunk, targetLang) {
  const prompt = createTranslationPrompt(chunk.value, targetLang);

  try {
    const response = await client.chat.completions.create({
      model: deployment,
      messages: [
        {
          role: "system",
          content:
            "You are a professional translator. Translate the following JSON content from English to " +
            targetLang +
            ".\n" +
            "Only translate string values within the JSON structure.\n" +
            "Important rules:\n" +
            "1. Keep exact JSON format\n" +
            '2. Use regular quotes (") not backticks\n' +
            "3. Do not translate:\n" +
            "   - JSON keys\n" +
            "   - URLs\n" +
            "   - Technical terms\n" +
            "   - File format names (MP3, MP4, M4A, etc.)\n" +
            "4. Return valid JSON only",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature: 0.3,
      max_tokens: 2000,
    });

    let content = response.choices[0].message.content.trim();

    // 清理额外的标记和引号
    content = content
      .replace(/^[\s\n]*```json[\s\n]*/, "") // 移除开头的 ```json
      .replace(/[\s\n]*```[\s\n]*$/, "") // 移除结尾的 ```
      .replace(/^"""\s*json\s*/, "") // 移除开头的 """json
      .replace(/\s*"""$/, "") // 移除结尾的 """
      .replace(/^['"`]|['"`]$/g, "") // 移除首尾的引号
      .trim();

    return {
      key: chunk.key,
      value: content,
      originalValue: chunk.value,
    };
  } catch (error) {
    console.error("Translation error:", error);
    throw error;
  }
}

// 修改创建翻译提示的函数
function createTranslationPrompt(chunk, targetLang) {
  return `Please translate the following content to ${targetLang}. 
  Maintain all JSON structure and only translate string values:
  
  ${chunk}
  
  Requirements:
  1. Keep all JSON keys unchanged
  2. Only translate string values
  3. Maintain all formatting and special characters
  4. Ensure translations are natural and accurate
  5. Keep all URLs and technical terms unchanged
  6. DO NOT translate the following terms, keep them exactly as they are: ${doNotTranslateTerms.join(", ")}`;
}

// 修改重组输出函数
async function reconstructOutput(translatedChunks) {
  const output = {};
  const errorCollector = createErrorCollector();

  for (const chunk of translatedChunks) {
    try {
      let valueStr = chunk.value;

      // 清理可能的 markdown 代码块标记
      if (typeof valueStr === "string") {
        valueStr = valueStr
          .replace(/^\s*```json\s*|\s*```\s*$/g, "")
          .replace(/`/g, '"')
          .trim();
      }

      try {
        // 首先尝试直接解析 JSON
        output[chunk.key] = JSON.parse(valueStr);
      } catch (parseError) {
        // 如果 JSON.parse 失败，尝试额外的清理和 eval
        valueStr = valueStr
          .replace(/[\u200B-\u200D\uFEFF]/g, "")
          .replace(/\n/g, " ")
          .trim();

        if (!valueStr.startsWith("{")) valueStr = "{" + valueStr;
        if (!valueStr.endsWith("}")) valueStr = valueStr + "}";

        try {
          output[chunk.key] = eval(`(${valueStr})`);
        } catch (evalError) {
          errorCollector.addError(
            chunk.key,
            chunk.originalValue,
            valueStr,
            evalError
          );
          output[chunk.key] = `[Translation Error: ${evalError.message}]`;
        }
      }
    } catch (error) {
      errorCollector.addError(
        chunk.key,
        chunk.originalValue,
        chunk.value,
        error
      );
      output[chunk.key] = `[Translation Error: ${error.message}]`;
    }
  }

  // 如果有错误，保存错误日志
  if (errorCollector.hasErrors()) {
    const errorPath = path.join(process.cwd(), "translation-errors.json");
    let existingErrors = {};

    try {
      try {
        const content = await fs.readFile(errorPath, "utf-8");
        existingErrors = JSON.parse(content);
      } catch (e) {
        // 如果文件不存在或无法读取，使用空对象
        console.warn("Could not read existing error file, creating new one");
      }

      // 合并新的错误
      const allErrors = {
        ...existingErrors,
        ...errorCollector.getErrors(),
      };

      await fs.writeFile(
        errorPath,
        JSON.stringify(allErrors, null, 2),
        "utf-8"
      );
      console.log(
        `\nSome translations had errors. Check translation-errors.json for details.`
      );
    } catch (error) {
      console.error("Failed to save error log:", error);
    }
  }

  // 返回字符串而不是 Promise，使用自定义格式保持与源文件一致
  return `export const KEYWORD_PAGES = ${customStringify(output)};`;
}

// 工具函数
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// 改进验证翻译函数
async function validateTranslation(original, translatedChunks) {
  try {
    // 验证每个翻译项的结构
    for (const chunk of translatedChunks) {
      try {
        const valueStr = chunk.value;
        let translatedObj;

        try {
          translatedObj = JSON.parse(valueStr);
        } catch (parseError) {
          // 如果 JSON.parse 失败，尝试清理后再解析
          const cleanedStr = valueStr
            .replace(/^\s*```json\s*|\s*```\s*$/g, "")
            .replace(/`/g, '"')
            .replace(/[\u200B-\u200D\uFEFF]/g, "")
            .replace(/\n/g, " ")
            .trim();

          if (!cleanedStr.startsWith("{")) {
            return false;
          }

          try {
            translatedObj = eval(`(${cleanedStr})`);
          } catch (evalError) {
            console.error(`Validation failed for key ${chunk.key}:`, evalError);
            return false;
          }
        }

        // 验证必要字段存在
        if (
          !translatedObj.title ||
          !translatedObj.displayTitle ||
          !translatedObj.description
        ) {
          console.error(`Missing required fields for key: ${chunk.key}`);
          return false;
        }

        // 验证字段类型
        if (
          typeof translatedObj.title !== "string" ||
          typeof translatedObj.displayTitle !== "string" ||
          typeof translatedObj.description !== "string"
        ) {
          console.error(`Invalid field types for key: ${chunk.key}`);
          return false;
        }

        // 验证字段不为空
        if (
          !translatedObj.title.trim() ||
          !translatedObj.displayTitle.trim() ||
          !translatedObj.description.trim()
        ) {
          console.error(`Empty required field found in key: ${chunk.key}`);
          return false;
        }

        // 验证嵌套结构（如果存在）
        if (translatedObj.howToUse) {
          if (!translatedObj.howToUse.title?.trim()) {
            console.error(`Empty howToUse title found in key: ${chunk.key}`);
            return false;
          }
        }

        // 验证数组结构（如果存在）
        if (translatedObj.features) {
          if (!Array.isArray(translatedObj.features)) {
            console.error(`Features is not an array in key: ${chunk.key}`);
            return false;
          }
          for (const feature of translatedObj.features) {
            if (!feature.title?.trim()) {
              console.error(`Empty feature title found in key: ${chunk.key}`);
              return false;
            }
          }
        }

        if (translatedObj.converters) {
          if (!Array.isArray(translatedObj.converters)) {
            console.error(`Converters is not an array in key: ${chunk.key}`);
            return false;
          }
          for (const converter of translatedObj.converters) {
            if (!converter.text?.trim() || !converter.link?.trim()) {
              console.error(
                `Empty converter text or link found in key: ${chunk.key}`
              );
              return false;
            }
          }
        }
      } catch (error) {
        console.error(`Validation error for key ${chunk.key}:`, error);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error("Validation failed:", error);
    return false;
  }
}

// 新增：更新特定键的翻译
async function updateTranslationKeys(
  filePath,
  keys,
  targetLangs,
  options = {}
) {
  try {
    const sourcePath = path.resolve(process.cwd(), filePath);
    const sourceDir = path.join(process.cwd(), "src/messages/en");
    const relativePath = path.relative(sourceDir, sourcePath);
    const dirPath = path.dirname(relativePath);
    const ext = path.extname(sourcePath);

    // 读取源文件
    const sourceContent = await fs.readFile(sourcePath, "utf-8");
    let sourceData;

    if (ext === ".js") {
      // 处理 JavaScript 文件（如 keywords.js）
      const jsonContent = sourceContent
        .replace(/export\s+const\s+KEYWORD_PAGES\s*=\s*/, "")
        .replace(/;?\s*$/, "");
      sourceData = eval("(" + jsonContent + ")");
    } else {
      // 处理 JSON 文件
      sourceData = JSON.parse(sourceContent);
    }

    // 提取需要翻译的键值对
    const keysToTranslate = {};
    for (const key of keys) {
      const value = getNestedValue(sourceData, key);
      if (value !== undefined) {
        keysToTranslate[key] = value;
      } else {
        console.warn(`Warning: Key "${key}" not found in source file`);
      }
    }

    if (Object.keys(keysToTranslate).length === 0) {
      console.error("Error: No valid keys found for translation");
      return;
    }

    for (const lang of targetLangs) {
      console.log(`\nUpdating keys for ${lang}...`);
      const targetDir = path.join(process.cwd(), "src/messages", lang);
      const targetPath = path.join(targetDir, relativePath);

      // 确保目标目录存在
      await fs.mkdir(path.dirname(targetPath), { recursive: true });

      // 翻译指定的键
      const translatedKeys = await translateKeys(keysToTranslate, lang);

      // 检查目标文件是否存在
      let targetContent = "";
      let targetData = {};
      let fileExists = false;

      try {
        targetContent = await fs.readFile(targetPath, "utf-8");
        fileExists = true;

        if (ext === ".js") {
          const jsonContent = targetContent
            .replace(/export\s+const\s+KEYWORD_PAGES\s*=\s*/, "")
            .replace(/;?\s*$/, "");
          targetData = eval("(" + jsonContent + ")");
        } else {
          targetData = JSON.parse(targetContent);
        }
      } catch (error) {
        // 如果文件不存在，创建一个空对象
        console.log(
          `Target file for ${lang} does not exist, creating new file`
        );
      }

      if (fileExists && ext === ".js") {
        // 对于 .js 文件，使用字符串替换来只更新指定的键
        let updatedContent = targetContent;

        for (const [key, value] of Object.entries(translatedKeys)) {
          // 创建新的键值对字符串，使用正确的缩进
          const valueStr = customStringify(value, 1); // 传入缩进级别
          const newKeyValueStr = `"${key}": ${valueStr}`;

          // 查找现有的键
          const keyRegex = new RegExp(
            `"${key.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}"\\s*:\\s*\\{[\\s\\S]*?\\}(?=\\s*,?\\s*(?:"[^"]*"\\s*:|\\}))`,
            "g"
          );

          if (keyRegex.test(updatedContent)) {
            // 更新现有键
            updatedContent = updatedContent.replace(keyRegex, newKeyValueStr);
          } else {
            // 添加新键 - 在文件末尾的 } 之前插入
            const insertPosition = updatedContent.lastIndexOf("};");
            if (insertPosition !== -1) {
              // 检查是否需要添加逗号
              const beforeInsert = updatedContent
                .substring(0, insertPosition)
                .trim();
              const needsComma =
                beforeInsert.endsWith("}") && !beforeInsert.endsWith(",");
              const comma = needsComma ? "," : "";
              const newEntry = `${comma}\n  ${newKeyValueStr}\n`;
              updatedContent =
                updatedContent.substring(0, insertPosition) +
                newEntry +
                updatedContent.substring(insertPosition);
            }
          }
        }

        await fs.writeFile(targetPath, updatedContent, "utf-8");
      } else {
        // 对于 JSON 文件或新文件，使用原有逻辑
        for (const [key, value] of Object.entries(translatedKeys)) {
          setNestedValue(targetData, key, value);
        }

        let outputContent;
        if (ext === ".js") {
          outputContent = `export const KEYWORD_PAGES = ${customStringify(targetData)};`;
        } else {
          outputContent = JSON.stringify(targetData, null, 2);
        }

        await fs.writeFile(targetPath, outputContent, "utf-8");
      }

      console.log(`Successfully updated keys in ${path.basename(targetPath)}`);

      // 如果更新的不是 index.js，则复制相关目录的 index.js
      if (path.basename(sourcePath) !== "index.js") {
        const sourceIndexPath = path.join(sourceDir, dirPath, "index.js");
        const targetIndexPath = path.join(targetDir, dirPath, "index.js");

        try {
          await fs.access(sourceIndexPath);
          await fs.copyFile(sourceIndexPath, targetIndexPath);
          console.log(`📄 Updated index.js in ${dirPath}`);
        } catch (error) {
          // 如果源 index.js 不存在，则跳过
          console.log(`No index.js found in ${dirPath}, skipping...`);
        }
      }
    }

    console.log("\nKey update completed!");
  } catch (error) {
    console.error("Key update failed:", error);
    throw error;
  }
}

// 辅助函数：获取嵌套对象的值
function getNestedValue(obj, path) {
  const keys = path.split(".");
  let current = obj;

  for (const key of keys) {
    if (current === undefined || current === null) {
      return undefined;
    }
    current = current[key];
  }

  return current;
}

// 辅助函数：设置嵌套对象的值
function setNestedValue(obj, path, value) {
  const keys = path.split(".");
  let current = obj;

  for (let i = 0; i < keys.length - 1; i++) {
    const key = keys[i];
    if (!(key in current)) {
      current[key] = {};
    }
    current = current[key];
  }

  current[keys[keys.length - 1]] = value;
  return obj;
}

// 翻译指定的键
async function translateKeys(keysObj, targetLang) {
  const result = {};

  for (const [key, value] of Object.entries(keysObj)) {
    console.log(`Translating key: ${key}`);

    if (typeof value === "string") {
      // 直接翻译字符串值
      result[key] = await translateText(value, targetLang);
    } else if (Array.isArray(value)) {
      // 翻译数组
      result[key] = await Promise.all(
        value.map((item) =>
          typeof item === "string"
            ? translateText(item, targetLang)
            : translateJson(item, targetLang)
        )
      );
    } else if (typeof value === "object" && value !== null) {
      // 递归翻译嵌套对象
      const nestedObj = await translateJson(value, targetLang);
      result[key] = nestedObj;
    } else {
      // 保持其他类型的值不变
      result[key] = value;
    }
  }

  return result;
}

// 自定义 JSON 序列化函数，保持与源文件相同的格式风格
function customStringify(obj, indent = 0) {
  const spaces = "  ".repeat(indent);
  const nextSpaces = "  ".repeat(indent + 1);

  if (obj === null) return "null";
  if (typeof obj === "undefined") return "undefined";
  if (typeof obj === "boolean") return obj.toString();
  if (typeof obj === "number") return obj.toString();
  if (typeof obj === "string") return JSON.stringify(obj);

  if (Array.isArray(obj)) {
    if (obj.length === 0) return "[]";
    const items = obj.map(
      (item) => nextSpaces + customStringify(item, indent + 1)
    );
    return "[\n" + items.join(",\n") + "\n" + spaces + "]";
  }

  if (typeof obj === "object") {
    const keys = Object.keys(obj);
    if (keys.length === 0) return "{}";

    const items = keys.map((key) => {
      const value = obj[key];
      // 只给包含特殊字符的键加引号
      const keyStr = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(key)
        ? key
        : JSON.stringify(key);
      const valueStr = customStringify(value, indent + 1);
      return nextSpaces + keyStr + ": " + valueStr;
    });

    return "{\n" + items.join(",\n") + "\n" + spaces + "}";
  }

  return JSON.stringify(obj);
}

// 运行主程序
main().catch(console.error);
